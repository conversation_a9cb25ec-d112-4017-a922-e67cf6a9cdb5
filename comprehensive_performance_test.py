#!/usr/bin/env python3
"""
Comprehensive Performance Test Suite for Akeneo Import Tool
Tests all optimizations and provides detailed performance benchmarks.
"""

import sys
import time
import json
import requests
import pandas as pd
from typing import Dict, Any, List
import tempfile
import os

class PerformanceTestSuite:
    """Comprehensive performance testing for all optimizations"""
    
    def __init__(self, base_url="http://localhost:5000"):
        self.base_url = base_url
        self.test_results = {}
        self.session = requests.Session()
        
    def log(self, message: str, level: str = "INFO"):
        """Log test messages with timestamps"""
        timestamp = time.strftime("%H:%M:%S")
        print(f"[{timestamp}] {level}: {message}")
    
    def test_excel_export_performance(self) -> Dict[str, Any]:
        """Test Excel export functionality and performance"""
        self.log("🧪 Testing Excel Export Performance...")
        
        try:
            # Create test job with sample data
            job_response = self.session.post(f"{self.base_url}/api/import/jobs", 
                                           json={"job_name": "Excel Export Test"})
            
            if job_response.status_code != 201:
                return {"success": False, "error": "Failed to create test job"}
            
            job_id = job_response.json()["job_id"]
            
            # Upload test data
            test_data = [{"col1": f"value_{i}", "col2": i, "col3": f"test_{i}"} for i in range(100)]
            
            # Create temporary CSV file
            with tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False) as f:
                pd.DataFrame(test_data).to_csv(f.name, index=False)
                csv_path = f.name
            
            try:
                with open(csv_path, 'rb') as f:
                    files = {'file': ('test_data.csv', f, 'text/csv')}
                    upload_response = self.session.post(
                        f"{self.base_url}/api/import/upload",
                        files=files,
                        data={'job_id': job_id}
                    )
                
                if upload_response.status_code != 200:
                    return {"success": False, "error": "Failed to upload test data"}
                
                # Test Excel export
                start_time = time.time()
                export_response = self.session.get(f"{self.base_url}/api/import/jobs/{job_id}/export-excel")
                export_time = time.time() - start_time
                
                if export_response.status_code == 200:
                    file_size = len(export_response.content)
                    return {
                        "success": True,
                        "export_time": round(export_time, 2),
                        "file_size_kb": round(file_size / 1024, 2),
                        "rows_exported": len(test_data),
                        "performance_score": "EXCELLENT" if export_time < 2 else "GOOD" if export_time < 5 else "NEEDS_IMPROVEMENT"
                    }
                else:
                    return {"success": False, "error": f"Export failed with status {export_response.status_code}"}
                    
            finally:
                os.unlink(csv_path)
                
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    def test_column_mapping_performance(self) -> Dict[str, Any]:
        """Test AI column mapping performance"""
        self.log("🧪 Testing Column Mapping Performance...")
        
        try:
            # Test data
            source_columns = [f"source_col_{i}" for i in range(20)]
            target_definitions = [
                {"column": f"target_col_{i}", "description": f"Target column {i} description"}
                for i in range(15)
            ]
            
            start_time = time.time()
            response = self.session.post(
                f"{self.base_url}/api/import/ai-map-columns",
                json={
                    "source_columns": source_columns,
                    "mapping_columns": target_definitions
                }
            )
            mapping_time = time.time() - start_time
            
            if response.status_code == 200:
                data = response.json()
                if data["success"]:
                    return {
                        "success": True,
                        "mapping_time": round(mapping_time, 2),
                        "server_processing_time": data.get("processing_time", "N/A"),
                        "mappings_found": data.get("mappings_found", 0),
                        "source_count": data.get("source_count", 0),
                        "target_count": data.get("target_count", 0),
                        "performance_score": "EXCELLENT" if mapping_time < 3 else "GOOD" if mapping_time < 8 else "NEEDS_IMPROVEMENT"
                    }
                else:
                    return {"success": False, "error": data.get("message", "Unknown error")}
            else:
                return {"success": False, "error": f"Request failed with status {response.status_code}"}
                
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    def test_bulk_processing_performance(self) -> Dict[str, Any]:
        """Test bulk processing performance"""
        self.log("🧪 Testing Bulk Processing Performance...")
        
        try:
            # Create test job
            job_response = self.session.post(f"{self.base_url}/api/import/jobs", 
                                           json={"job_name": "Bulk Processing Test"})
            
            if job_response.status_code != 201:
                return {"success": False, "error": "Failed to create test job"}
            
            job_id = job_response.json()["job_id"]
            
            # Test high-performance bulk processing
            test_logic = "Transform @col1 to uppercase"
            
            start_time = time.time()
            response = self.session.post(
                f"{self.base_url}/api/import/jobs/{job_id}/high-performance-bulk-apply-logic",
                json={
                    "logic": test_logic,
                    "column_name": "test_column",
                    "references": {"notes": "Performance test"}
                }
            )
            processing_time = time.time() - start_time
            
            if response.status_code == 200:
                data = response.json()
                if data["success"]:
                    return {
                        "success": True,
                        "processing_time": round(processing_time, 2),
                        "server_processing_time": data.get("processing_time", "N/A"),
                        "rows_per_second": data.get("rows_per_second", "N/A"),
                        "total_processed": data.get("total_processed", 0),
                        "performance_score": "EXCELLENT" if processing_time < 5 else "GOOD" if processing_time < 15 else "NEEDS_IMPROVEMENT"
                    }
                else:
                    return {"success": False, "error": data.get("message", "Unknown error")}
            else:
                return {"success": False, "error": f"Request failed with status {response.status_code}"}
                
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    def test_performance_monitoring(self) -> Dict[str, Any]:
        """Test performance monitoring endpoints"""
        self.log("🧪 Testing Performance Monitoring...")
        
        try:
            # Create test job
            job_response = self.session.post(f"{self.base_url}/api/import/jobs", 
                                           json={"job_name": "Monitoring Test"})
            
            if job_response.status_code != 201:
                return {"success": False, "error": "Failed to create test job"}
            
            job_id = job_response.json()["job_id"]
            
            # Test real-time metrics
            start_time = time.time()
            metrics_response = self.session.get(f"{self.base_url}/api/import/jobs/{job_id}/real-time-metrics")
            metrics_time = time.time() - start_time
            
            # Test performance summary
            start_time = time.time()
            summary_response = self.session.get(f"{self.base_url}/api/import/jobs/{job_id}/performance-metrics?days=1")
            summary_time = time.time() - start_time
            
            if metrics_response.status_code == 200 and summary_response.status_code == 200:
                return {
                    "success": True,
                    "metrics_response_time": round(metrics_time, 2),
                    "summary_response_time": round(summary_time, 2),
                    "metrics_available": metrics_response.json().get("success", False),
                    "summary_available": summary_response.json().get("success", False),
                    "performance_score": "EXCELLENT" if max(metrics_time, summary_time) < 1 else "GOOD"
                }
            else:
                return {"success": False, "error": "Monitoring endpoints failed"}
                
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    def run_all_tests(self) -> Dict[str, Any]:
        """Run all performance tests"""
        self.log("🚀 Starting Comprehensive Performance Test Suite")
        self.log("=" * 60)
        
        tests = [
            ("Excel Export", self.test_excel_export_performance),
            ("Column Mapping", self.test_column_mapping_performance),
            ("Bulk Processing", self.test_bulk_processing_performance),
            ("Performance Monitoring", self.test_performance_monitoring)
        ]
        
        results = {}
        overall_score = 0
        total_tests = len(tests)
        
        for test_name, test_func in tests:
            self.log(f"Running {test_name} test...")
            try:
                result = test_func()
                results[test_name] = result
                
                if result["success"]:
                    score = result.get("performance_score", "UNKNOWN")
                    if score == "EXCELLENT":
                        overall_score += 3
                    elif score == "GOOD":
                        overall_score += 2
                    else:
                        overall_score += 1
                    
                    self.log(f"✅ {test_name}: {score}")
                else:
                    self.log(f"❌ {test_name}: FAILED - {result.get('error', 'Unknown error')}")
                    
            except Exception as e:
                self.log(f"❌ {test_name}: EXCEPTION - {str(e)}")
                results[test_name] = {"success": False, "error": str(e)}
        
        # Calculate overall performance grade
        max_score = total_tests * 3
        percentage = (overall_score / max_score) * 100
        
        if percentage >= 85:
            overall_grade = "EXCELLENT"
        elif percentage >= 70:
            overall_grade = "GOOD"
        elif percentage >= 50:
            overall_grade = "FAIR"
        else:
            overall_grade = "NEEDS_IMPROVEMENT"
        
        summary = {
            "overall_grade": overall_grade,
            "overall_score": f"{overall_score}/{max_score}",
            "percentage": round(percentage, 1),
            "test_results": results,
            "recommendations": self._generate_recommendations(results)
        }
        
        self.log("=" * 60)
        self.log(f"🎯 Overall Performance Grade: {overall_grade} ({percentage:.1f}%)")
        
        return summary
    
    def _generate_recommendations(self, results: Dict[str, Any]) -> List[str]:
        """Generate performance improvement recommendations"""
        recommendations = []
        
        for test_name, result in results.items():
            if not result.get("success"):
                recommendations.append(f"Fix {test_name} functionality - currently failing")
            elif result.get("performance_score") == "NEEDS_IMPROVEMENT":
                recommendations.append(f"Optimize {test_name} performance - currently slow")
        
        if not recommendations:
            recommendations.append("All systems performing optimally! 🚀")
        
        return recommendations

def main():
    """Run the comprehensive performance test suite"""
    print("🎯 Akeneo Import Tool - Comprehensive Performance Test Suite")
    print("=" * 70)
    
    # Check if server is running
    try:
        response = requests.get("http://localhost:5000/", timeout=5)
        print("✅ Server is running")
    except requests.exceptions.RequestException:
        print("❌ Server is not running. Please start the Flask application first.")
        return 1
    
    # Run tests
    test_suite = PerformanceTestSuite()
    results = test_suite.run_all_tests()
    
    # Display detailed results
    print("\n📊 Detailed Test Results:")
    print("=" * 70)
    
    for test_name, result in results["test_results"].items():
        print(f"\n{test_name}:")
        if result["success"]:
            for key, value in result.items():
                if key not in ["success", "error"]:
                    print(f"  {key}: {value}")
        else:
            print(f"  ❌ Error: {result.get('error', 'Unknown error')}")
    
    # Display recommendations
    print(f"\n💡 Recommendations:")
    for rec in results["recommendations"]:
        print(f"  • {rec}")
    
    print(f"\n🏆 Final Grade: {results['overall_grade']} ({results['percentage']}%)")
    
    return 0 if results["overall_grade"] in ["EXCELLENT", "GOOD"] else 1

if __name__ == "__main__":
    sys.exit(main())
