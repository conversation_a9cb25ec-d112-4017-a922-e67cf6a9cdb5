import os
import requests
import json
import asyncio
import aiohttp
import time
from concurrent.futures import ThreadPoolExecutor, as_completed
from typing import List, Dict, Any, Optional, Union
from dotenv import load_dotenv, set_key, find_dotenv

class AkeneoAPI:
    """
    Unified class for interacting with the Akeneo PIM API.
    Handles authentication, token management, and product queries.
    """
    
    def __init__(self):
        """Initialize the API client by loading environment variables"""
        load_dotenv(override=True)
        self.endpoint = os.getenv("AKENEO_ENDPOINT")
        self.token = os.getenv("TOKEN")

        # Performance optimization settings
        self.session = requests.Session()
        self.session.headers.update({'Content-Type': 'application/json'})

        # Rate limiting settings
        self.rate_limit_delay = 0.1  # Start with 100ms delay
        self.max_rate_limit_delay = 5.0  # Max 5 seconds
        self.rate_limit_backoff = 1.5  # Exponential backoff multiplier
        self.last_request_time = 0

        # Batch processing settings
        self.default_batch_size = 50
        self.max_batch_size = 100

        # Refresh token automatically if not available
        if not self.token:
            self.refresh_token()
    
    def refresh_token(self):
        """Fetch a new access token and save it to the .env file"""
        client_id = os.getenv("AKENEO_CLIENT_ID")
        client_secret = os.getenv("AKENEO_CLIENT_SECRET")
        username = os.getenv("AKENEO_USERNAME")
        password = os.getenv("AKENEO_PASSWORD")
        
        # Find the .env file path
        dotenv_path = find_dotenv()
        if not dotenv_path:
            dotenv_path = os.path.join(os.getcwd(), ".env")
        
        if not all([self.endpoint, client_id, client_secret, username, password]):
            print("Error: Missing one or more environment variables (AKENEO_ENDPOINT, AKENEO_CLIENT_ID, AKENEO_CLIENT_SECRET, AKENEO_USERNAME, AKENEO_PASSWORD).")
            return None
            
        token_url = f"{self.endpoint.rstrip('/')}/api/oauth/v1/token"
        
        payload = {
            "grant_type": "password",
            "client_id": client_id,
            "client_secret": client_secret,
            "username": username,
            "password": password
        }
        
        headers = {
            "Content-Type": "application/x-www-form-urlencoded"
        }
        
        try:
            print(f"Requesting token from: {token_url}")
            response = requests.post(token_url, data=payload, headers=headers)
            response.raise_for_status()
            
            token_data = response.json()
            access_token = token_data.get("access_token")
            expires_in = token_data.get("expires_in")
            
            if access_token:
                print(f"New Access Token: {access_token}")
                if expires_in:
                    print(f"Expires in: {expires_in} seconds")
                
                # Save the new token to the .env file
                success = set_key(dotenv_path, "TOKEN", access_token)
                if success:
                    print(f"Token successfully saved to {dotenv_path}")
                else:
                    print(f"Error: Failed to save token to {dotenv_path}.")
                
                self.token = access_token
                return access_token
            else:
                print(f"Error: Could not retrieve access token. Response: {token_data}")
                return None
                
        except requests.exceptions.HTTPError as http_err:
            print(f"HTTP error occurred: {http_err}")
            try:
                print(f"Response content: {response.content.decode()}")
            except AttributeError:
                print(f"Response content: {response.content}")
            return None
        except requests.exceptions.RequestException as req_err:
            print(f"Request error occurred: {req_err}")
            return None
        except ValueError as json_err:
            print(f"JSON decode error: {json_err}")
            try:
                print(f"Response content: {response.content.decode()}")
            except AttributeError:
                print(f"Response content: {response.content}")
            return None
    
    def get_headers(self):
        """Get the authorization headers for API requests"""
        return {
            "Authorization": f"Bearer {self.token}",
            "Content-Type": "application/json"
        }

    def _handle_rate_limiting(self, response):
        """Handle rate limiting with adaptive delays"""
        if response.status_code == 429:  # Too Many Requests
            retry_after = response.headers.get('Retry-After')
            if retry_after:
                delay = float(retry_after)
            else:
                delay = self.rate_limit_delay
                self.rate_limit_delay = min(self.rate_limit_delay * self.rate_limit_backoff, self.max_rate_limit_delay)

            print(f"Rate limited. Waiting {delay} seconds...")
            time.sleep(delay)
            return True
        else:
            # Reset rate limit delay on successful request
            self.rate_limit_delay = 0.1
            return False

    def _make_request_with_retry(self, method, url, max_retries=3, **kwargs):
        """Make HTTP request with retry logic and rate limiting"""
        for attempt in range(max_retries):
            try:
                # Ensure minimum delay between requests
                current_time = time.time()
                time_since_last = current_time - self.last_request_time
                if time_since_last < 0.05:  # 50ms minimum delay
                    time.sleep(0.05 - time_since_last)

                response = self.session.request(method, url, **kwargs)
                self.last_request_time = time.time()

                # Handle rate limiting
                if self._handle_rate_limiting(response):
                    continue  # Retry after rate limit delay

                response.raise_for_status()
                return response

            except requests.exceptions.RequestException as e:
                if attempt == max_retries - 1:
                    raise
                print(f"Request failed (attempt {attempt + 1}/{max_retries}): {e}")
                time.sleep(2 ** attempt)  # Exponential backoff

        raise Exception(f"Failed to make request after {max_retries} attempts")
    
    def get_attribute_options(self, attribute_code, limit=100):
        """
        Get all options for a specific attribute (like HERSTELLER)
        
        Args:
            attribute_code: The attribute code to query options for
            limit: Maximum number of options to return
            
        Returns:
            List of attribute options or empty list on error
        """
        if not self.token:
            print("No token available. Refreshing...")
            self.refresh_token()
            
        api_url = f"{self.endpoint.rstrip('/')}/api/rest/v1/attributes/{attribute_code}/options"
        params = {"limit": limit}
        
        print(f"\nQuerying options for attribute '{attribute_code}' from: {api_url}")
        
        try:
            response = requests.get(api_url, headers=self.get_headers(), params=params)
            response.raise_for_status()
            
            data = response.json()
            
            if '_embedded' in data and 'items' in data['_embedded']:
                options = data['_embedded']['items']
                print(f"Found {len(options)} options for {attribute_code}")
                return options
            else:
                print(f"No options found for attribute {attribute_code}")
                return []
                
        except requests.exceptions.RequestException as e:
            print(f"Error getting attribute options: {e}")
            if hasattr(e, 'response') and e.response is not None:
                print(f"Response status: {e.response.status_code}")
                print(f"Response body: {e.response.text}")
            return []
    
    def query_products_by_attribute(self, attribute_code, attribute_value, operator="IN", limit=10):
        """
        Query products filtered by a specific attribute and value
        
        Args:
            attribute_code: The attribute code to filter on (e.g. 'HERSTELLER')
            attribute_value: The value to filter by
            operator: The operator to use (e.g. 'IN', '=', etc.)
            limit: Maximum number of products to return
            
        Returns:
            List of product objects or empty list on error
        """
        if not self.token:
            print("No token available. Refreshing...")
            self.refresh_token()
            
        api_url = f"{self.endpoint.rstrip('/')}/api/rest/v1/products-uuid"
        
        # Format the value correctly for the operator
        actual_value = [attribute_value] if operator == "IN" else attribute_value
        
        # Build the search filter in the correct structure
        search_filter = {
            attribute_code: [{
                "operator": operator,
                "value": actual_value
            }]
        }
        
        params = {
            "search": json.dumps(search_filter),
            "limit": limit,
            "with_count": "true"
        }
        
        print(f"\nQuerying products for {attribute_code}='{attribute_value}' from: {api_url}")
        print(f"Using search filter: {json.dumps(search_filter)}")
        
        try:
            response = requests.get(api_url, headers=self.get_headers(), params=params)
            response.raise_for_status()
            
            data = response.json()
            
            if '_embedded' in data and 'items' in data['_embedded']:
                products = data['_embedded']['items']
                total_count = data.get('total_items', 'unknown')
                print(f"Found {len(products)} products out of {total_count} total")
                return products
            else:
                print("No products found in the response")
                return []
                
        except requests.exceptions.RequestException as e:
            print(f"Error querying products: {e}")
            if hasattr(e, 'response') and e.response is not None:
                print(f"Response status: {e.response.status_code}")
                print(f"Response body: {e.response.text}")
            
            # If token expired, try refreshing and running again
            if hasattr(e, 'response') and e.response.status_code == 401:
                print("Token may have expired. Attempting to refresh...")
                if self.refresh_token():
                    print("Token refreshed, retrying query...")
                    return self.query_products_by_attribute(attribute_code, attribute_value, operator, limit)
            
            return []
    
    def query_products_by_uuids(self, uuids, attributes="all", channel=None, locales=None):
        """
        Retrieve detailed product information for a list of UUIDs
        
        Args:
            uuids: List of product UUIDs to retrieve
            attributes: "all" for all attributes, or list of specific attribute codes
            channel: Specific channel to retrieve data for (scope)
            locales: Specific locales to retrieve data for
            
        Returns:
            List of product objects with detailed information or empty list on error
        """
        if not self.token:
            print("No token available. Refreshing...")
            self.refresh_token()
            
        # Use the search endpoint for getting multiple products by UUIDs
        api_url = f"{self.endpoint.rstrip('/')}/api/rest/v1/products-uuid/search"
        
        # Build request payload
        payload = {
            "search": {
                "uuid": [{"operator": "IN", "value": uuids}]
            }
        }
        
        # Add additional parameters if provided
        if attributes != "all" and isinstance(attributes, list):
            payload["attributes"] = attributes
        elif attributes == "all":
            payload["attributes"] = "all"
            
        if channel:
            payload["scope"] = channel
            
        if locales:
            payload["locales"] = locales
            
        print(f"\nRetrieving detailed information for {len(uuids)} products from: {api_url}")
        print(f"Using payload: {json.dumps(payload)}")
        
        try:
            response = requests.post(api_url, headers=self.get_headers(), json=payload)
            response.raise_for_status()
            
            data = response.json()
            
            if '_embedded' in data and 'items' in data['_embedded']:
                products = data['_embedded']['items']
                print(f"Successfully retrieved details for {len(products)} products")
                return products
            else:
                print("No products found in the response")
                return []
                
        except requests.exceptions.RequestException as e:
            print(f"Error retrieving product details: {e}")
            if hasattr(e, 'response') and e.response is not None:
                print(f"Response status: {e.response.status_code}")
                print(f"Response body: {e.response.text}")
            
            # If token expired, try refreshing and running again
            if hasattr(e, 'response') and e.response.status_code == 401:
                print("Token may have expired. Attempting to refresh...")
                if self.refresh_token():
                    print("Token refreshed, retrying query...")
                    return self.query_products_by_uuids(uuids, attributes, channel, locales)
            
            return []
    
    def query_products_by_supplier(self, supplier_code, limit=10):
        """
        Query products by supplier (HERSTELLER code)
        Convenience method that wraps query_products_by_attribute
        
        Args:
            supplier_code: The HERSTELLER code to filter by
            limit: Maximum number of products to return
            
        Returns:
            List of product objects or empty list on error
        """
        return self.query_products_by_attribute("HERSTELLER", supplier_code, "IN", limit)
    
    def query_products_with_complex_filters(self, hersteller_code, empty_attributes=None, channel="holzprofi24.de", limit=10):
        """
        Query products with complex filtering capabilities:
        - Filter by HERSTELLER (manufacturer)
        - Filter for attributes that are empty
        - Specify channel for localized attributes
        
        Args:
            hersteller_code: The HERSTELLER (manufacturer) code to filter by
            empty_attributes: List of attribute codes that should be empty
            channel: The channel to use for scoped attributes (default: holzprofi24.de)
            limit: Maximum number of products to return
            
        Returns:
            List of product objects with all attributes or empty list on error
        """
        if not self.token:
            print("No token available. Refreshing...")
            self.refresh_token()
            
        api_url = f"{self.endpoint.rstrip('/')}/api/rest/v1/products-uuid"
        
        # Build the search filter
        search_filter = {
            "HERSTELLER": [{"operator": "IN", "value": [hersteller_code]}]
        }
        
        # Add empty attribute filters if specified
        if empty_attributes and isinstance(empty_attributes, list):
            for attr in empty_attributes:
                search_filter[attr] = [{"operator": "EMPTY"}]
        
        params = {
            "search": json.dumps(search_filter),
            "limit": limit,
            "with_count": "true",
            "scope": channel
        }
        
        print(f"\nQuerying products with complex filters:")
        print(f"- HERSTELLER = '{hersteller_code}'")
        if empty_attributes:
            print(f"- Empty attributes: {', '.join(empty_attributes)}")
        print(f"- Channel: {channel}")
        print(f"- API URL: {api_url}")
        print(f"- Using search filter: {json.dumps(search_filter)}")
        
        try:
            response = requests.get(api_url, headers=self.get_headers(), params=params)
            response.raise_for_status()
            
            data = response.json()
            
            if '_embedded' in data and 'items' in data['_embedded']:
                products = data['_embedded']['items']
                total_count = data.get('total_items', 'unknown')
                print(f"Found {len(products)} products out of {total_count} total")
                return products
            else:
                print("No products found in the response")
                return []
                
        except requests.exceptions.RequestException as e:
            print(f"Error querying products: {e}")
            if hasattr(e, 'response') and e.response is not None:
                print(f"Response status: {e.response.status_code}")
                print(f"Response body: {e.response.text}")
            
            # If token expired, try refreshing and running again
            if hasattr(e, 'response') and e.response.status_code == 401:
                print("Token may have expired. Attempting to refresh...")
                if self.refresh_token():
                    print("Token refreshed, retrying query...")
                    return self.query_products_with_complex_filters(hersteller_code, empty_attributes, channel, limit)
            
            return []
    
    def list_supplier_codes(self, limit=100):
        """
        Get all available supplier (HERSTELLER) codes
        Convenience method that wraps get_attribute_options
        
        Args:
            limit: Maximum number of supplier codes to return
            
        Returns:
            List of supplier options or empty list on error
        """
        return self.get_attribute_options("HERSTELLER", limit)
    
    def list_attributes(self, limit=100, page=1):
        """
        Get all available attribute codes/metadata from Akeneo
        
        Args:
            limit: Maximum number of attributes to return per page
            page: Page number to retrieve (starting at 1)
            
        Returns:
            List of attribute objects or empty list on error
        """
        if not self.token:
            print("No token available. Refreshing...")
            self.refresh_token()
            
        api_url = f"{self.endpoint.rstrip('/')}/api/rest/v1/attributes"
        params = {
            "limit": limit,
            "page": page
        }
        
        print(f"\nQuerying available attributes from: {api_url} (page {page}, limit {limit})")
        
        try:
            response = requests.get(api_url, headers=self.get_headers(), params=params)
            response.raise_for_status()
            
            data = response.json()
            
            if '_embedded' in data and 'items' in data['_embedded']:
                attributes = data['_embedded']['items']
                print(f"Found {len(attributes)} attributes on page {page}")
                return attributes
            else:
                print(f"No attributes found on page {page}")
                return []
                
        except requests.exceptions.RequestException as e:
            print(f"Error getting attributes: {e}")
            if hasattr(e, 'response') and e.response is not None:
                print(f"Response status: {e.response.status_code}")
                print(f"Response body: {e.response.text}")
            
            # If token expired, try refreshing and running again
            if hasattr(e, 'response') and e.response is not None and e.response.status_code == 401:
                print("Token may have expired. Attempting to refresh...")
                if self.refresh_token():
                    print("Token refreshed, retrying list_attributes...")
                    return self.list_attributes(limit, page) # Retry the request
            return []
    
    def list_channels(self, limit=20):
        """
        Get all available channels from Akeneo
        
        Args:
            limit: Maximum number of channels to return
            
        Returns:
            List of channel objects or empty list on error
        """
        if not self.token:
            print("No token available. Refreshing...")
            self.refresh_token()
            
        api_url = f"{self.endpoint.rstrip('/')}/api/rest/v1/channels"
        params = {"limit": limit}
        
        print(f"\nQuerying available channels from: {api_url}")
        
        try:
            response = requests.get(api_url, headers=self.get_headers(), params=params)
            response.raise_for_status()
            
            data = response.json()
            
            if '_embedded' in data and 'items' in data['_embedded']:
                channels = data['_embedded']['items']
                print(f"Found {len(channels)} channels")
                return channels
            else:
                print(f"No channels found")
                return []
                
        except requests.exceptions.RequestException as e:
            print(f"Error getting channels: {e}")
            if hasattr(e, 'response') and e.response is not None:
                print(f"Response status: {e.response.status_code}")
                print(f"Response body: {e.response.text}")
            return []
    
    def list_families(self, limit=100, search=None):
        """
        Get a list of all product families
        
        Args:
            limit: Maximum number of families to return
            search: Search filter as JSON string
            
        Returns:
            List of family objects or empty list on error
        """
        if not self.token:
            print("No token available. Refreshing...")
            self.refresh_token()
            
        api_url = f"{self.endpoint.rstrip('/')}/api/rest/v1/families"
        params = {"limit": limit}
        
        if search:
            params["search"] = search
            
        try:
            response = requests.get(api_url, headers=self.get_headers(), params=params)
            response.raise_for_status()
            
            data = response.json()
            
            if '_embedded' in data and 'items' in data['_embedded']:
                families = data['_embedded']['items']
                return families
            else:
                return []
                
        except requests.exceptions.RequestException as e:
            print(f"Error listing families: {e}")
            return []
    
    def get_family(self, family_code):
        """
        Get details for a specific family by its code
        
        Args:
            family_code: The code of the family to retrieve
            
        Returns:
            Family object or None on error
        """
        if not self.token:
            print("No token available. Refreshing...")
            self.refresh_token()
            
        api_url = f"{self.endpoint.rstrip('/')}/api/rest/v1/families/{family_code}"
        
        try:
            response = requests.get(api_url, headers=self.get_headers())
            response.raise_for_status()
            
            family = response.json()
            return family
                
        except requests.exceptions.RequestException as e:
            print(f"Error getting family details: {e}")
            return None
    
    def list_categories(self, limit_per_page=100, search=None, with_count=False):
        """
        List all categories with pagination.

        Args:
            limit_per_page: Number of categories per page (default 100, max usually 100).
            search: Optional search filter.
            with_count: Optional, to include total count in response.

        Returns:
            List of all category objects or empty list on error.
        """
        if not self.token:
            print("No token available. Refreshing...")
            if not self.refresh_token():
                print("Failed to refresh token. Aborting list_categories.")
                return []

        all_categories = []
        page = 1
        
        # Ensure the endpoint is correctly formed
        base_api_url = f"{self.endpoint.rstrip('/')}/api/rest/v1/categories"
        
        print(f"\nListing categories from: {base_api_url}")

        while True:
            params = {"page": page, "limit": limit_per_page}
            if search:
                params["search"] = json.dumps(search)
            if with_count:
                params["with_count"] = "true"

            try:
                # print(f"Fetching page {page} with params: {params}")
                response = requests.get(base_api_url, headers=self.get_headers(), params=params)
                response.raise_for_status()
                data = response.json()

                if '_embedded' in data and 'items' in data['_embedded']:
                    current_page_categories = data['_embedded']['items']
                    all_categories.extend(current_page_categories)
                    # print(f"Fetched {len(current_page_categories)} categories on page {page}. Total: {len(all_categories)}")
                    
                    # Check for a 'next' link to continue pagination
                    if '_links' in data and 'next' in data['_links']:
                        page += 1
                    else:
                        # No 'next' link, so we've reached the end
                        break 
                else:
                    # No items found on this page, or unexpected format
                    # print(f"No items found on page {page} or unexpected format. Data: {data}")
                    break
                    
            except requests.exceptions.HTTPError as http_err:
                print(f"HTTP error occurred while fetching categories (page {page}): {http_err}")
                if hasattr(http_err, 'response') and http_err.response is not None:
                    print(f"Response status: {http_err.response.status_code}")
                    print(f"Response body: {http_err.response.text}")
                # If a 422 error occurs, it might be an issue with the request itself, stop paginating.
                if http_err.response.status_code == 422:
                    print("Unprocessable Entity error encountered. Stopping pagination.")
                    # Return what we have so far, or an empty list if the first page failed.
                    return all_categories if all_categories else []
                # For other HTTP errors, break the loop.
                break
            except requests.exceptions.RequestException as req_err:
                print(f"Request error occurred while fetching categories (page {page}): {req_err}")
                break
            except ValueError as json_err: # Includes JSONDecodeError
                print(f"JSON decode error while fetching categories (page {page}): {json_err}")
                print(f"Response content: {response.text if response else 'No response'}")
                break
        
        print(f"Finished fetching categories. Total categories retrieved: {len(all_categories)}")
        return all_categories
    
    def list_attribute_groups(self, limit=100, search=None, with_count=False):
        """
        Get a list of all attribute groups (used for product groups)
        
        Args:
            limit: Maximum number of attribute groups to return
            search: Search filter as JSON string
            with_count: Whether to include the total count in the response
            
        Returns:
            List of attribute group objects or empty list on error
        """
        if not self.token:
            print("No token available. Refreshing...")
            self.refresh_token()
            
        api_url = f"{self.endpoint.rstrip('/')}/api/rest/v1/attribute-groups"
        params = {"limit": limit}
        
        if search:
            params["search"] = search
            
        if with_count:
            params["with_count"] = "true"
            
        try:
            response = requests.get(api_url, headers=self.get_headers(), params=params)
            response.raise_for_status()
            
            data = response.json()
            
            if '_embedded' in data and 'items' in data['_embedded']:
                groups = data['_embedded']['items']
                return groups
            else:
                return []
                
        except requests.exceptions.RequestException as e:
            print(f"Error listing attribute groups: {e}")
            return []
    
    def list_locales(self, limit=100, search=None, with_count=False):
        """
        Get a list of all locales in the PIM
        
        Args:
            limit: Maximum number of locales to return
            search: Search filter as JSON string
            with_count: Whether to include the total count in the response
            
        Returns:
            List of locale objects or empty list on error
        """
        if not self.token:
            print("No token available. Refreshing...")
            self.refresh_token()
            
        api_url = f"{self.endpoint.rstrip('/')}/api/rest/v1/locales"
        params = {"limit": limit}
        
        if search:
            params["search"] = search
            
        if with_count:
            params["with_count"] = "true"
            
        try:
            response = requests.get(api_url, headers=self.get_headers(), params=params)
            response.raise_for_status()
            
            data = response.json()
            
            if '_embedded' in data and 'items' in data['_embedded']:
                locales = data['_embedded']['items']
                return locales
            else:
                return []
                
        except requests.exceptions.RequestException as e:
            print(f"Error listing locales: {e}")
            return []
    
    def get_attribute_option_label(self, option, preferred_locales=None):
        """
        Extract the best available label from an attribute option
        
        Args:
            option: The attribute option object
            preferred_locales: List of preferred locale codes in order of preference
                              (e.g. ['de_DE', 'en_US'])
        
        Returns:
            The best available label string
        """
        if not preferred_locales:
            preferred_locales = ['de_DE', 'en_US', 'en_GB']  # Default preference order
            
        labels = option.get('labels', {})
        
        # Try each preferred locale in order
        for locale in preferred_locales:
            if locale in labels and labels[locale]:
                return labels[locale]
        
        # If no preferred locale found, take the first available label
        if labels:
            return next(iter(labels.values()), 'No Label')
        
        return 'No Label'
    
    def display_product_details(self, products, max_display=3, supplier_name=None):
        """
        Display details of products in a human-readable format
        
        Args:
            products: List of product objects from the API
            max_display: Maximum number of products to display in detail
            supplier_name: Optional name of the supplier to display
        """
        if not products:
            print("No products to display")
            return
            
        if supplier_name:
            print(f"\nProducts from supplier: {supplier_name}")
            
        print(f"\nProduct details (first {min(max_display, len(products))}):")
        for i, product in enumerate(products[:max_display], 1):
            uuid = product.get('uuid', 'N/A')
            identifier = product.get('identifier', 'N/A')
            values = product.get('values', {})
            
            # Try to get a product name from different common attributes
            name = "Unknown"
            for name_attr in ['name', 'label', 'ARTIKELNAME', 'TITLE']:
                if name_attr in values:
                    name_data = values[name_attr]
                    if isinstance(name_data, list) and len(name_data) > 0:
                        name_item = name_data[0]
                        if isinstance(name_item, dict) and 'data' in name_item:
                            name = name_item['data']
                            break
            
            # Get Hersteller value if available
            hersteller = "Unknown"
            if 'HERSTELLER' in values:
                hersteller_data = values['HERSTELLER']
                if isinstance(hersteller_data, list) and len(hersteller_data) > 0:
                    hersteller_item = hersteller_data[0]
                    if isinstance(hersteller_item, dict) and 'data' in hersteller_item:
                        hersteller = hersteller_item['data']
            
            print(f"{i}. UUID: {uuid}")
            print(f"   Identifier: {identifier}")
            print(f"   Name: {name}")
            print(f"   Manufacturer: {hersteller}")
            print("   ---")
            
        if len(products) > max_display:
            print(f"... and {len(products) - max_display} more products")

    def search_products_generic(self, payload):
        """
        Generic product search with a flexible payload.

        Args:
            payload (dict): The search payload. Expected to contain keys like 'limit',
                            'with_count', 'scope', and 'search' (the Akeneo search filter dict).
        
        Returns:
            dict: A dictionary containing {'items': [], 'total_count': 0, 'page': 1} or None on error.
        """
        if not self.token:
            print("No token available. Refreshing...")
            if not self.refresh_token():
                print("Failed to refresh token. Aborting search.")
                return None
        
        api_url = f"{self.endpoint.rstrip('/')}/api/rest/v1/products-uuid"
        
        # Parameters for the GET request
        params = {}
        if 'limit' in payload:
            params['limit'] = payload['limit']
        if 'with_count' in payload:
            params['with_count'] = payload['with_count']
        if 'scope' in payload:
            params['scope'] = payload['scope']
        if 'page' in payload: # For pagination
            params['page'] = payload['page']
        if 'search' in payload and payload['search']:
            params['search'] = json.dumps(payload['search'])
        
        print(f"\nQuerying products (generic) from: {api_url}")
        print(f"Using params: {params}") # Careful with logging raw search dict if too large

        try:
            response = requests.get(api_url, headers=self.get_headers(), params=params)
            response.raise_for_status()
            
            data = response.json()
            
            products = []
            total_count = 0
            current_page = 1

            if '_embedded' in data and 'items' in data['_embedded']:
                products = data['_embedded']['items']
            
            if 'items_count' in data: # Akeneo often returns total items in 'items_count' when with_count=true
                total_count = data.get('items_count', 0)
            elif 'total_items' in data: # Fallback, older Akeneo versions might use this
                 total_count = data.get('total_items', 0)
            else: # If no count, assume count is length of items for this page
                total_count = len(products)

            if '_links' in data and 'self' in data['_links'] and 'href' in data['_links']['self']:
                # Try to parse page from self link, e.g., "/api/rest/v1/products?page=1&limit=10..."
                try:
                    url_params = dict(x.split('=') for x in data['_links']['self']['href'].split('?')[1].split('&'))
                    current_page = int(url_params.get('page', 1))
                except ValueError:
                    pass # Could not parse page
            
            return {
                'items': products,
                'total_count': total_count,
                'page': current_page
            }
                
        except requests.exceptions.HTTPError as http_err:
            print(f"HTTP error occurred during generic product search: {http_err}")
            print(f"Response status: {http_err.response.status_code}")
            print(f"Response body: {http_err.response.text}")
            if http_err.response.status_code == 401:
                print("Token may have expired. Attempting to refresh...")
                if self.refresh_token():
                    print("Token refreshed, retrying generic search...")
                    return self.search_products_generic(payload) # Retry with new token
            return None # Indicate error
        except requests.exceptions.RequestException as e:
            print(f"Error during generic product search: {e}")
            if hasattr(e, 'response') and e.response is not None:
                print(f"Response status: {e.response.status_code}")
                print(f"Response body: {e.response.text}")
            return None # Indicate error
        except json.JSONDecodeError as json_err:
            print(f"JSON decode error during generic product search: {json_err}")
            print(f"Response content: {response.text if 'response' in locals() else 'N/A'}")
            return None

    def bulk_create_products(self, products: List[Dict[str, Any]], batch_size: Optional[int] = None) -> Dict[str, Any]:
        """
        Create multiple products in bulk using Akeneo's PATCH endpoint.

        Args:
            products: List of product dictionaries to create
            batch_size: Number of products per batch (default: self.default_batch_size)

        Returns:
            Dict with success/failure counts and details
        """
        if not products:
            return {"success": True, "created": 0, "errors": []}

        batch_size = batch_size or self.default_batch_size
        batch_size = min(batch_size, self.max_batch_size)

        if not self.token:
            print("No token available. Refreshing...")
            if not self.refresh_token():
                return {"success": False, "error": "Failed to refresh token"}

        api_url = f"{self.endpoint.rstrip('/')}/api/rest/v1/products-uuid"

        total_created = 0
        total_errors = []

        print(f"Creating {len(products)} products in batches of {batch_size}...")

        # Process in batches
        for i in range(0, len(products), batch_size):
            batch = products[i:i + batch_size]
            batch_num = (i // batch_size) + 1
            total_batches = (len(products) + batch_size - 1) // batch_size

            print(f"Processing batch {batch_num}/{total_batches} ({len(batch)} products)...")

            try:
                # Prepare PATCH request body (JSONL format)
                jsonl_data = "\n".join([json.dumps(product) for product in batch])

                headers = self.get_headers()
                headers["Content-Type"] = "application/vnd.akeneo.collection+json"

                response = self._make_request_with_retry(
                    "PATCH",
                    api_url,
                    headers=headers,
                    data=jsonl_data
                )

                # Parse response
                response_lines = response.text.strip().split('\n')
                batch_created = 0
                batch_errors = []

                for line in response_lines:
                    if line.strip():
                        try:
                            result = json.loads(line)
                            if result.get('status_code') in [201, 204]:  # Created or Updated
                                batch_created += 1
                            else:
                                batch_errors.append({
                                    'identifier': result.get('identifier', 'unknown'),
                                    'status_code': result.get('status_code'),
                                    'message': result.get('message', 'Unknown error')
                                })
                        except json.JSONDecodeError:
                            batch_errors.append({'error': f'Invalid response line: {line}'})

                total_created += batch_created
                total_errors.extend(batch_errors)

                print(f"Batch {batch_num} completed: {batch_created} created, {len(batch_errors)} errors")

            except Exception as e:
                error_msg = f"Batch {batch_num} failed: {str(e)}"
                print(error_msg)
                total_errors.append({'batch': batch_num, 'error': error_msg})

        return {
            "success": len(total_errors) == 0,
            "created": total_created,
            "total_processed": len(products),
            "errors": total_errors
        }

    def bulk_update_products(self, products: List[Dict[str, Any]], batch_size: Optional[int] = None) -> Dict[str, Any]:
        """
        Update multiple products in bulk using Akeneo's PATCH endpoint.

        Args:
            products: List of product dictionaries to update (must include uuid or identifier)
            batch_size: Number of products per batch (default: self.default_batch_size)

        Returns:
            Dict with success/failure counts and details
        """
        return self.bulk_create_products(products, batch_size)  # Same endpoint handles both create and update

    async def async_bulk_create_products(self, products: List[Dict[str, Any]], batch_size: Optional[int] = None, max_concurrent: int = 3) -> Dict[str, Any]:
        """
        Asynchronously create multiple products in bulk for maximum performance.

        Args:
            products: List of product dictionaries to create
            batch_size: Number of products per batch (default: self.default_batch_size)
            max_concurrent: Maximum number of concurrent requests

        Returns:
            Dict with success/failure counts and details
        """
        if not products:
            return {"success": True, "created": 0, "errors": []}

        batch_size = batch_size or self.default_batch_size
        batch_size = min(batch_size, self.max_batch_size)

        if not self.token:
            print("No token available. Refreshing...")
            if not self.refresh_token():
                return {"success": False, "error": "Failed to refresh token"}

        api_url = f"{self.endpoint.rstrip('/')}/api/rest/v1/products-uuid"

        # Split into batches
        batches = [products[i:i + batch_size] for i in range(0, len(products), batch_size)]

        print(f"Creating {len(products)} products in {len(batches)} batches with max {max_concurrent} concurrent requests...")

        async def process_batch(session, batch, batch_num):
            """Process a single batch asynchronously"""
            try:
                # Prepare PATCH request body (JSONL format)
                jsonl_data = "\n".join([json.dumps(product) for product in batch])

                headers = self.get_headers()
                headers["Content-Type"] = "application/vnd.akeneo.collection+json"

                async with session.patch(api_url, headers=headers, data=jsonl_data) as response:
                    response_text = await response.text()

                    # Parse response
                    response_lines = response_text.strip().split('\n')
                    batch_created = 0
                    batch_errors = []

                    for line in response_lines:
                        if line.strip():
                            try:
                                result = json.loads(line)
                                if result.get('status_code') in [201, 204]:  # Created or Updated
                                    batch_created += 1
                                else:
                                    batch_errors.append({
                                        'identifier': result.get('identifier', 'unknown'),
                                        'status_code': result.get('status_code'),
                                        'message': result.get('message', 'Unknown error')
                                    })
                            except json.JSONDecodeError:
                                batch_errors.append({'error': f'Invalid response line: {line}'})

                    print(f"Batch {batch_num} completed: {batch_created} created, {len(batch_errors)} errors")
                    return batch_created, batch_errors

            except Exception as e:
                error_msg = f"Batch {batch_num} failed: {str(e)}"
                print(error_msg)
                return 0, [{'batch': batch_num, 'error': error_msg}]

        # Process batches concurrently
        connector = aiohttp.TCPConnector(limit=max_concurrent)
        timeout = aiohttp.ClientTimeout(total=300)  # 5 minute timeout

        async with aiohttp.ClientSession(connector=connector, timeout=timeout) as session:
            # Create semaphore to limit concurrent requests
            semaphore = asyncio.Semaphore(max_concurrent)

            async def process_with_semaphore(batch, batch_num):
                async with semaphore:
                    return await process_batch(session, batch, batch_num)

            # Execute all batches concurrently
            tasks = [process_with_semaphore(batch, i + 1) for i, batch in enumerate(batches)]
            results = await asyncio.gather(*tasks, return_exceptions=True)

        # Aggregate results
        total_created = 0
        total_errors = []

        for result in results:
            if isinstance(result, Exception):
                total_errors.append({'error': str(result)})
            else:
                created, errors = result
                total_created += created
                total_errors.extend(errors)

        return {
            "success": len(total_errors) == 0,
            "created": total_created,
            "total_processed": len(products),
            "errors": total_errors
        }


# Command-line interface for testing the API
def main():
    # Create API client instance
    akeneo = AkeneoAPI()
    
    # Main menu
    while True:
        print("\n=== Akeneo API Test Menu ===")
        print("1. Refresh access token")
        print("2. List supplier codes (HERSTELLER options)")
        print("3. Query products by supplier (HERSTELLER)")
        print("4. Test multiple suppliers to find those with products")
        print("5. Query detailed product information by UUIDs")
        print("6. Query products with complex filters")
        print("7. List product families")
        print("0. Exit")
        
        choice = input("\nEnter your choice (0-7): ")
        
        if choice == "0":
            print("Exiting.")
            break
            
        elif choice == "1":
            akeneo.refresh_token()
            
        elif choice == "2":
            options = akeneo.list_supplier_codes()
            if options:
                print("\nAvailable supplier options:")
                for i, option in enumerate(options[:20], 1):
                    code = option.get('code', 'N/A')
                    label = akeneo.get_attribute_option_label(option)
                    print(f"{i}. Code: {code}, Label: {label}")
                if len(options) > 20:
                    print(f"... and {len(options) - 20} more options")
            
        elif choice == "3":
            # First, list supplier options for reference
            options = akeneo.list_supplier_codes()
            if options:
                print("\nAvailable supplier options:")
                for i, option in enumerate(options[:10], 1):
                    code = option.get('code', 'N/A')
                    label = akeneo.get_attribute_option_label(option)
                    print(f"{i}. Code: {code}, Label: {label}")
                if len(options) > 10:
                    print(f"... and {len(options) - 10} more options")
                
                # Allow selection by index or direct code entry
                selection = input("\nEnter supplier number (1-10) or code directly: ")
                
                # Determine the supplier code and name
                supplier_code = ""
                supplier_name = ""
                
                # Check if the user entered a number (index) or a code directly
                try:
                    index = int(selection) - 1
                    if 0 <= index < len(options) and index < 10:  # We only displayed up to 10
                        supplier_code = options[index]['code']
                        supplier_name = akeneo.get_attribute_option_label(options[index])
                    else:
                        print("Invalid selection. Please enter a number between 1-10.")
                        continue
                except ValueError:
                    # User entered a code directly
                    supplier_code = selection
                    # Try to find the name
                    for option in options:
                        if option.get('code') == supplier_code:
                            supplier_name = akeneo.get_attribute_option_label(option)
                            break
                
                if not supplier_code:
                    print("No valid supplier code provided.")
                    continue
                    
                # Get limit
                limit = input(f"Enter maximum number of products to retrieve for {supplier_code} (default 10): ")
                
                try:
                    limit = int(limit) if limit else 10
                except ValueError:
                    limit = 10
                    print("Invalid limit, using default value of 10")
                
                # Query products
                products = akeneo.query_products_by_supplier(supplier_code, limit)
                
                # Display results with supplier name
                akeneo.display_product_details(products, max_display=limit, supplier_name=supplier_name)
                
                # If products were found, offer to get detailed information
                if products:
                    detail_choice = input("\nDo you want to retrieve detailed information for these products? (y/n, default: n): ").lower()
                    if detail_choice == 'y':
                        # Extract UUIDs
                        uuids = [product.get('uuid') for product in products if 'uuid' in product]
                        if uuids:
                            # Ask for attributes
                            attr_choice = input("\nRetrieve all attributes? (y/n, default: y): ").lower()
                            
                            attributes = "all"
                            if attr_choice == 'n':
                                attr_input = input("Enter specific attribute codes (comma-separated): ")
                                if attr_input:
                                    attributes = [attr.strip() for attr in attr_input.split(',')]
                            
                            # Get detailed product information
                            detailed_products = akeneo.query_products_by_uuids(uuids, attributes)
                            
                            # Display the results
                            if detailed_products:
                                # Ask if user wants to save to file
                                save_choice = input("\nSave detailed product information to file? (y/n, default: n): ").lower()
                                
                                if save_choice == 'y':
                                    filename = input("Enter filename (default: detailed_products.json): ") or "detailed_products.json"
                                    with open(filename, 'w', encoding='utf-8') as f:
                                        json.dump(detailed_products, f, indent=2, ensure_ascii=False)
                                    print(f"Saved detailed product information to {filename}")
                                
                                # Display summary
                                print(f"\nRetrieved detailed information for {len(detailed_products)} products")
                                akeneo.display_product_details(detailed_products, max_display=3)
            
        elif choice == "4":
            # Test multiple suppliers to find ones with products
            options = akeneo.list_supplier_codes()
            if not options:
                print("No supplier options found. Cannot test.")
                continue
                
            # Test indices across the range of available options
            test_count = min(len(options), 100)
            step = max(1, test_count // 5)  # Test about 5 suppliers evenly distributed
            
            test_indices = list(range(0, test_count, step))
            if test_indices and test_indices[-1] != test_count - 1:
                test_indices.append(test_count - 1)  # Add the last one if not already included
                
            print(f"\nTesting {len(test_indices)} suppliers for products...")
            
            suppliers_with_products = []
            
            for index in test_indices:
                if index < len(options):
                    supplier_code = options[index]['code']
                    print(f"\n--- Testing supplier: {supplier_code} ---")
                    
                    # Query with small limit for faster testing
                    products = akeneo.query_products_by_supplier(supplier_code, 5)
                    
                    if products:
                        print(f"Found {len(products)} products for {supplier_code}")
                        suppliers_with_products.append((supplier_code, len(products)))
            
            # Summary of results
            print("\n=== Testing Results ===")
            if suppliers_with_products:
                print(f"Found {len(suppliers_with_products)} suppliers with products:")
                for code, count in suppliers_with_products:
                    print(f"- {code}: {count} products")
            else:
                print("No suppliers with products found in the tested sample.")
                
        elif choice == "5":
            # First get products from a supplier to extract UUIDs
            supplier_code = input("\nEnter supplier code to get product UUIDs from (or leave empty to enter UUIDs manually): ")
            
            uuids = []
            if supplier_code:
                # Query products from the supplier
                products = akeneo.query_products_by_supplier(supplier_code, 10)
                if products:
                    # Extract UUIDs
                    uuids = [product.get('uuid') for product in products if 'uuid' in product]
                    print(f"Extracted {len(uuids)} UUIDs from supplier {supplier_code}")
            else:
                # Manual entry
                uuid_input = input("\nEnter UUIDs (comma-separated): ")
                if uuid_input:
                    # Properly parse the comma-separated UUIDs, removing any whitespace
                    uuids = [uuid.strip() for uuid in uuid_input.split(',')]
                    print(f"Parsed {len(uuids)} UUIDs from input")
            
            if not uuids:
                print("No UUIDs available. Cannot proceed.")
                continue
                
            # Ask for attributes
            attr_choice = input("\nRetrieve all attributes? (y/n, default: y): ").lower()
            
            attributes = "all"
            if attr_choice == 'n':
                attr_input = input("Enter specific attribute codes (comma-separated): ")
                if attr_input:
                    attributes = [attr.strip() for attr in attr_input.split(',')]
            
            # Get detailed product information
            detailed_products = akeneo.query_products_by_uuids(uuids, attributes)
            
            # Display the results
            if detailed_products:
                # Ask if user wants to save to file
                save_choice = input("\nSave detailed product information to file? (y/n, default: n): ").lower()
                
                if save_choice == 'y':
                    filename = input("Enter filename (default: detailed_products.json): ") or "detailed_products.json"
                    with open(filename, 'w', encoding='utf-8') as f:
                        json.dump(detailed_products, f, indent=2, ensure_ascii=False)
                    print(f"Saved detailed product information to {filename}")
                
                # Display summary
                print(f"\nRetrieved detailed information for {len(detailed_products)} products")
                akeneo.display_product_details(detailed_products, max_display=3)
            
        elif choice == "6":
            # Query products with complex filters
            
            # First, list supplier options for reference
            options = akeneo.list_supplier_codes()
            if options:
                print("\nAvailable supplier options:")
                for i, option in enumerate(options[:10], 1):
                    code = option.get('code', 'N/A')
                    label = akeneo.get_attribute_option_label(option)
                    print(f"{i}. Code: {code}, Label: {label}")
                if len(options) > 10:
                    print(f"... and {len(options) - 10} more options")
                
                # Allow selection by index or direct code entry
                selection = input("\nEnter supplier number (1-10) or code directly: ")
                
                # Determine the supplier code and name
                supplier_code = ""
                supplier_name = ""
                
                # Check if the user entered a number (index) or a code directly
                try:
                    index = int(selection) - 1
                    if 0 <= index < len(options) and index < 10:  # We only displayed up to 10
                        supplier_code = options[index]['code']
                        supplier_name = akeneo.get_attribute_option_label(options[index])
                    else:
                        print("Invalid selection. Please enter a number between 1-10.")
                        continue
                except ValueError:
                    # User entered a code directly
                    supplier_code = selection
                    # Try to find the name
                    for option in options:
                        if option.get('code') == supplier_code:
                            supplier_name = akeneo.get_attribute_option_label(option)
                            break
                
                if not supplier_code:
                    print("No valid supplier code provided.")
                    continue
                
                # Fetch and display available attributes
                attributes = akeneo.list_attributes(limit=100)
                if attributes:
                    print("\nAvailable attributes (select which ones should be empty):")
                    
                    # Filter out some system attributes and sort alphabetically
                    filtered_attributes = sorted(
                        [attr for attr in attributes if not attr.get('code', '').startswith('pim_')], 
                        key=lambda x: x.get('code', '')
                    )
                    
                    # Display attributes in a numbered list
                    for i, attr in enumerate(filtered_attributes[:20], 1):
                        code = attr.get('code', 'N/A')
                        type_value = attr.get('type', 'unknown')
                        label = attr.get('labels', {}).get('en_US', code)
                        print(f"{i}. Code: {code}, Type: {type_value}, Label: {label}")
                    
                    if len(filtered_attributes) > 20:
                        print(f"... and {len(filtered_attributes) - 20} more attributes")
                    
                    # Ask user for selection
                    attr_selection = input("\nEnter attribute numbers to check for emptiness (comma-separated, 'all' for all shown, or leave blank for none): ")
                    
                    empty_attributes = None
                    if attr_selection.strip().lower() == 'all':
                        # Select all displayed attributes
                        empty_attributes = [attr.get('code') for attr in filtered_attributes[:20] if attr.get('code')]
                        print(f"Selected all {len(empty_attributes)} displayed attributes")
                    elif attr_selection.strip():
                        # Parse selected indices
                        empty_attributes = []
                        try:
                            indices = [int(idx.strip()) - 1 for idx in attr_selection.split(',') if idx.strip()]
                            for idx in indices:
                                if 0 <= idx < len(filtered_attributes) and idx < 20:  # We only displayed up to 20
                                    code = filtered_attributes[idx].get('code')
                                    if code:
                                        empty_attributes.append(code)
                            print(f"Selected {len(empty_attributes)} attributes to check for emptiness")
                        except ValueError:
                            print("Invalid selection format. Using no empty attribute filters.")
                
                # Get available channels and allow selection
                channels = akeneo.list_channels()
                channel = None
                
                if channels:
                    print("\nAvailable channels:")
                    for i, ch in enumerate(channels, 1):
                        code = ch.get('code', 'N/A')
                        label = ch.get('labels', {}).get('en_US', code)
                        print(f"{i}. Code: {code}, Label: {label}")
                    
                    # Allow selection by index or direct code entry
                    ch_selection = input("\nEnter channel number or code directly (default: first channel): ")
                    
                    if ch_selection.strip():
                        try:
                            ch_index = int(ch_selection) - 1
                            if 0 <= ch_index < len(channels):
                                channel = channels[ch_index].get('code')
                            else:
                                print(f"Invalid channel index. Using first channel.")
                        except ValueError:
                            # User entered a code directly
                            ch_selection = ch_selection.strip()
                            # Check if the code exists
                            for ch in channels:
                                if ch.get('code') == ch_selection:
                                    channel = ch_selection
                                    break
                            if not channel:
                                print(f"Channel code '{ch_selection}' not found. Using first channel.")
                    
                    # If no valid selection, use the first channel
                    if not channel and channels:
                        channel = channels[0].get('code')
                        print(f"Using channel: {channel}")
                else:
                    # No channels found, prompt for manual entry
                    channel = input("\nNo channels found. Enter channel code manually: ")
                
                if not channel:
                    print("No valid channel provided. Cannot proceed.")
                    continue
                
                # Get limit
                limit_input = input(f"\nEnter maximum number of products to retrieve (default 10): ")
                try:
                    limit = int(limit_input) if limit_input else 10
                except ValueError:
                    limit = 10
                    print("Invalid limit, using default value of 10")
                
                # Query products
                products = akeneo.query_products_with_complex_filters(supplier_code, empty_attributes, channel, limit)
                
                # Display results with supplier name
                akeneo.display_product_details(products, max_display=limit, supplier_name=supplier_name)
                
                # If products were found, offer to save to file
                if products:
                    save_choice = input("\nSave product information to file? (y/n, default: n): ").lower()
                    
                    if save_choice == 'y':
                        filename = input("Enter filename (default: filtered_products.json): ") or "filtered_products.json"
                        with open(filename, 'w', encoding='utf-8') as f:
                            json.dump(products, f, indent=2, ensure_ascii=False)
                        print(f"Saved product information to {filename}")
            
        elif choice == "7":
            # List product families
            limit = input(f"Enter maximum number of families to retrieve (default 100): ")
            try:
                limit = int(limit) if limit else 100
            except ValueError:
                limit = 100
                print("Invalid limit, using default value of 100")
            
            search = input("Enter search filter (JSON string format, or leave empty for no search): ")
            
            families = akeneo.list_families(limit, search)
            if families:
                print(f"\nFound {len(families)} families:")
                for i, family in enumerate(families, 1):
                    code = family.get('code', 'N/A')
                    attributes_count = len(family.get('attributes', []))
                    attribute_as_label = family.get('attribute_as_label', 'N/A')
                    labels = family.get('labels', {})
                    label = labels.get('en_US', code) if labels else code
                    
                    print(f"{i}. Code: {code}, Label: {label}")
                    print(f"   Attributes: {attributes_count}, Label attribute: {attribute_as_label}")
                    print("   ---")
            else:
                print("No families found.")
            
        else:
            print("Invalid choice. Please try again.")


if __name__ == "__main__":
    main() 