import json
import uuid
import hashlib
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, List, Union
from config.redis_config import redis_jobs, redis_llm, redis_sessions, redis_tasks, redis_config

class RedisJobManager:
    """Redis-based job management replacing file-based JSON storage"""
    
    def __init__(self):
        self.redis = redis_jobs
        self.ttl = redis_config.ttl_config
    
    def create_job(self, job_name: str, original_filename: Optional[str] = None) -> str:
        """Create a new job in Redis"""
        job_id = str(uuid.uuid4())
        timestamp = datetime.utcnow().isoformat() + 'Z'
        
        job_metadata = {
            'id': job_id,
            'name': job_name,
            'status': 'draft',
            'original_filename': original_filename or '',  # Convert None to empty string
            'created_at': timestamp,
            'updated_at': timestamp,
            'row_count': '0',  # Convert to string
            'column_count': '0'  # Convert to string
        }
        
        # Store job metadata
        self.redis.hset(f'job:{job_id}:meta', mapping=job_metadata)
        self.redis.expire(f'job:{job_id}:meta', self.ttl['job_metadata'])
        
        # Initialize empty UI state
        ui_state = {
            'deactivated_fields': [],
            'ui_column_order': [],
            'user_notes': '',
            'selected_worksheet': ''
        }
        self.redis.set(f'job:{job_id}:ui_state', json.dumps(ui_state))
        self.redis.expire(f'job:{job_id}:ui_state', self.ttl['ui_state'])
        
        # Add to jobs index using numeric timestamp as score
        timestamp_score = datetime.utcnow().timestamp()
        self.redis.zadd('jobs:index', {job_id: timestamp_score})
        
        return job_id
    
    def get_job_metadata(self, job_id: str) -> Optional[Dict[str, Any]]:
        """Get job metadata from Redis"""
        metadata = self.redis.hgetall(f'job:{job_id}:meta')
        if not metadata:
            return None
        
        # Convert bytes to strings and handle type conversion
        result = {}
        for k, v in metadata.items():
            key = k.decode('utf-8') if isinstance(k, bytes) else k
            value = v.decode('utf-8') if isinstance(v, bytes) else v
            
            # Convert numeric fields back to integers
            if key in ['row_count', 'column_count']:
                try:
                    result[key] = int(value) if value else 0
                except (ValueError, TypeError):
                    result[key] = 0
            else:
                result[key] = value
        
        return result
    
    def update_job_metadata(self, job_id: str, updates: Dict[str, Any]) -> bool:
        """Update job metadata in Redis"""
        try:
            updates['updated_at'] = datetime.utcnow().isoformat() + 'Z'
            
            # Convert None values to empty strings and ensure all values are serializable
            clean_updates = {}
            for key, value in updates.items():
                if value is None:
                    clean_updates[key] = ''
                elif isinstance(value, (int, float)):
                    clean_updates[key] = str(value)
                else:
                    clean_updates[key] = value
            
            self.redis.hset(f'job:{job_id}:meta', mapping=clean_updates)
            self.redis.expire(f'job:{job_id}:meta', self.ttl['job_metadata'])
            return True
        except Exception as e:
            print(f"Error updating job metadata: {e}")
            return False
    
    def delete_job(self, job_id: str) -> bool:
        """Delete a job and all its data from Redis"""
        try:
            # Get all keys related to this job
            pattern = f'job:{job_id}:*'
            keys = self.redis.keys(pattern)
            
            if keys:
                self.redis.delete(*keys)
            
            # Remove from jobs index
            self.redis.zrem('jobs:index', job_id)
            
            return True
        except Exception as e:
            print(f"Error deleting job: {e}")
            return False
    
    def list_jobs(self, limit: int = 100, offset: int = 0) -> List[Dict[str, Any]]:
        """List all jobs with pagination"""
        # Get job IDs from sorted set (most recent first)
        job_ids = self.redis.zrevrange('jobs:index', offset, offset + limit - 1)
        
        jobs = []
        for job_id in job_ids:
            if isinstance(job_id, bytes):
                job_id = job_id.decode('utf-8')
            
            metadata = self.get_job_metadata(job_id)
            if metadata:
                jobs.append(metadata)
        
        return jobs
    
    def store_mapping_definitions(self, job_id: str, mapping_definitions: List[Dict[str, Any]]) -> bool:
        """Store mapping definitions for a job"""
        try:
            key = f'job:{job_id}:mapping_definitions'
            self.redis.set(key, json.dumps(mapping_definitions))
            self.redis.expire(key, self.ttl['job_metadata'])
            return True
        except Exception as e:
            print(f"Error storing mapping definitions: {e}")
            return False
    
    def get_mapping_definitions(self, job_id: str) -> List[Dict[str, Any]]:
        """Get mapping definitions for a job"""
        try:
            key = f'job:{job_id}:mapping_definitions'
            data = self.redis.get(key)
            if data:
                if isinstance(data, bytes):
                    data = data.decode('utf-8')
                return json.loads(data)
            return []
        except Exception as e:
            print(f"Error getting mapping definitions: {e}")
            return []
    
    def store_source_columns(self, job_id: str, source_columns: List[str]) -> bool:
        """Store source columns for a job"""
        try:
            key = f'job:{job_id}:source_columns'
            self.redis.set(key, json.dumps(source_columns))
            self.redis.expire(key, self.ttl['job_metadata'])
            return True
        except Exception as e:
            print(f"Error storing source columns: {e}")
            return False
    
    def get_source_columns(self, job_id: str) -> List[str]:
        """Get source columns for a job"""
        try:
            key = f'job:{job_id}:source_columns'
            data = self.redis.get(key)
            if data:
                if isinstance(data, bytes):
                    data = data.decode('utf-8')
                return json.loads(data)
            return []
        except Exception as e:
            print(f"Error getting source columns: {e}")
            return []
    
    def store_validation_configs(self, job_id: str, validation_configs: Dict[str, Any]) -> bool:
        """Store validation configurations for a job"""
        try:
            key = f'job:{job_id}:validation_configs'
            self.redis.set(key, json.dumps(validation_configs))
            self.redis.expire(key, self.ttl['job_metadata'])
            return True
        except Exception as e:
            print(f"Error storing validation configs: {e}")
            return False
    
    def get_validation_configs(self, job_id: str) -> Dict[str, Any]:
        """Get validation configurations for a job"""
        try:
            key = f'job:{job_id}:validation_configs'
            data = self.redis.get(key)
            if data:
                if isinstance(data, bytes):
                    data = data.decode('utf-8')
                return json.loads(data)
            return {}
        except Exception as e:
            print(f"Error getting validation configs: {e}")
            return {}

class RedisJobDataManager:
    """Redis-based job data management with pagination"""
    
    def __init__(self, job_id: str):
        self.job_id = job_id
        self.redis = redis_jobs
        self.ttl = redis_config.ttl_config
        self.page_size = 20  # Default page size
    
    def store_source_data(self, data: List[Dict[str, Any]], page_size: int = 50) -> bool:
        """Store source data in Redis with pagination and compression for better performance"""
        try:
            import gzip
            import pickle

            total_rows = len(data)
            total_pages = (total_rows + page_size - 1) // page_size

            # Use pipeline for bulk operations
            pipe = self.redis.pipeline()

            # Store data in pages
            for page_num in range(total_pages):
                start_idx = page_num * page_size
                end_idx = min(start_idx + page_size, total_rows)
                page_data = data[start_idx:end_idx]

                # Add row indices to each row
                for i, row in enumerate(page_data):
                    row['_row_index'] = start_idx + i

                # Compress data for better performance
                compressed_data = gzip.compress(pickle.dumps(page_data))

                # Store page
                page_key = f'job:{self.job_id}:source:page:{page_num + 1}'
                pipe.set(page_key, compressed_data)
                pipe.expire(page_key, self.ttl['job_data_pages'])

            # Execute all operations at once
            pipe.execute()
            
            # Update job metadata
            job_manager = RedisJobManager()
            job_manager.update_job_metadata(self.job_id, {
                'row_count': total_rows,
                'column_count': len(data[0].keys()) if data else 0,
                'total_pages': total_pages
            })
            
            return True
        except Exception as e:
            print(f"Error storing source data: {e}")
            return False
    
    def get_data_page(self, page: int, limit: int = 50) -> Dict[str, Any]:
        """Get a specific page of data with compression support"""
        page_key = f'job:{self.job_id}:source:page:{page}'
        page_data = self.redis.get(page_key)

        if not page_data:
            return {'rows': [], 'pagination': {'page': page, 'limit': limit, 'total': 0}}

        try:
            # Check if data is compressed
            metadata = RedisJobManager().get_job_metadata(self.job_id)
            is_compressed = metadata.get('compression_enabled') == 'true' if metadata else False

            if is_compressed:
                import gzip
                import pickle
                all_rows = pickle.loads(gzip.decompress(page_data))
            else:
                all_rows = json.loads(page_data)

            # Limit the rows returned based on the requested limit
            # This allows for sub-page batch sizes (e.g., getting 1 row from a 50-row page)
            rows = all_rows[:limit] if limit < len(all_rows) else all_rows

            # Get total count from job metadata
            total_rows = int(metadata.get('row_count', 0)) if metadata else 0

            return {
                'rows': rows,
                'pagination': {
                    'page': page,
                    'limit': limit,
                    'total': total_rows,
                    'has_next': page * limit < total_rows,
                    'has_prev': page > 1
                }
            }
        except Exception as e:
            print(f"Error getting data page: {e}")
            return {'rows': [], 'pagination': {'page': page, 'limit': limit, 'total': 0}}
    
    def update_cell(self, row_index: int, column_name: str, value: Any) -> bool:
        """Update a single cell in the dataset"""
        try:
            # Calculate which page contains this row
            page_num = (row_index // self.page_size) + 1
            page_key = f'job:{self.job_id}:source:page:{page_num}'

            page_data = self.redis.get(page_key)
            if not page_data:
                return False

            # Check if data is compressed
            metadata = RedisJobManager().get_job_metadata(self.job_id)
            is_compressed = metadata.get('compression_enabled') == 'true' if metadata else False

            if is_compressed:
                import gzip
                import pickle
                rows = pickle.loads(gzip.decompress(page_data))
            else:
                rows = json.loads(page_data)

            # Find the row within the page
            page_row_index = row_index % self.page_size
            if page_row_index < len(rows):
                rows[page_row_index][column_name] = value

                # Update the page with compression if enabled
                if is_compressed:
                    compressed_data = gzip.compress(pickle.dumps(rows))
                    self.redis.set(page_key, compressed_data)
                else:
                    self.redis.set(page_key, json.dumps(rows))
                self.redis.expire(page_key, self.ttl['job_data_pages'])

                return True

            return False
        except Exception as e:
            print(f"Error updating cell: {e}")
            return False

    def bulk_update_cells(self, updates: List[Dict[str, Any]]) -> bool:
        """
        Update multiple cells in bulk for better performance.

        Args:
            updates: List of dicts with 'row_index', 'column_name', 'value'
        """
        try:
            # Group updates by page for efficient processing
            page_updates = {}

            for update in updates:
                row_index = update['row_index']
                page_num = (row_index // self.page_size) + 1

                if page_num not in page_updates:
                    page_updates[page_num] = []

                page_updates[page_num].append({
                    'page_row_index': row_index % self.page_size,
                    'column_name': update['column_name'],
                    'value': update['value']
                })

            # Check if data is compressed
            metadata = RedisJobManager().get_job_metadata(self.job_id)
            is_compressed = metadata.get('compression_enabled') == 'true' if metadata else False

            # Use pipeline for bulk operations
            pipe = self.redis.pipeline()

            # Process each page
            for page_num, page_update_list in page_updates.items():
                page_key = f'job:{self.job_id}:source:page:{page_num}'
                page_data = self.redis.get(page_key)

                if not page_data:
                    continue

                # Load page data
                if is_compressed:
                    import gzip
                    import pickle
                    rows = pickle.loads(gzip.decompress(page_data))
                else:
                    rows = json.loads(page_data)

                # Apply all updates for this page
                for update in page_update_list:
                    page_row_index = update['page_row_index']
                    if page_row_index < len(rows):
                        rows[page_row_index][update['column_name']] = update['value']

                # Store updated page
                if is_compressed:
                    compressed_data = gzip.compress(pickle.dumps(rows))
                    pipe.set(page_key, compressed_data)
                else:
                    pipe.set(page_key, json.dumps(rows))
                pipe.expire(page_key, self.ttl['job_data_pages'])

            # Execute all updates at once
            pipe.execute()
            return True

        except Exception as e:
            print(f"Error in bulk update cells: {e}")
            return False

class RedisTransformationManager:
    """Redis-based transformation results management"""
    
    def __init__(self, job_id: str):
        self.job_id = job_id
        self.redis = redis_jobs
        self.ttl = redis_config.ttl_config
    
    def store_column_transformations(self, column_name: str, transformations: List[Dict[str, Any]]) -> bool:
        """Store transformation results for a column"""
        try:
            key = f'job:{self.job_id}:transforms:{column_name}'
            self.redis.set(key, json.dumps(transformations))
            self.redis.expire(key, self.ttl['job_data_pages'])
            return True
        except Exception as e:
            print(f"Error storing transformations: {e}")
            return False
    
    def get_column_transformations(self, column_name: str) -> List[Dict[str, Any]]:
        """Get transformation results for a column"""
        key = f'job:{self.job_id}:transforms:{column_name}'
        data = self.redis.get(key)
        
        if not data:
            return []
        
        try:
            parsed_data = json.loads(data)
            
            # Ensure we return a list of dictionaries
            if not isinstance(parsed_data, list):
                print(f"Warning: Expected list but got {type(parsed_data)}, returning empty list")
                return []
            
            # Filter out any non-dict entries
            valid_transformations = []
            for item in parsed_data:
                if isinstance(item, dict):
                    valid_transformations.append(item)
                else:
                    print(f"Warning: Skipping invalid transformation entry: {type(item)} - {item}")
            
            return valid_transformations
        except Exception as e:
            print(f"Error getting transformations: {e}")
            return []
    
    def update_single_transformation(self, column_name: str, row_index: int, transformation: Dict[str, Any]) -> bool:
        """Update a single transformation result"""
        try:
            transformations = self.get_column_transformations(column_name)
            
            # Ensure transformations is a list of dictionaries
            if not isinstance(transformations, list):
                print(f"Warning: Invalid transformations data type: {type(transformations)}, resetting to empty list")
                transformations = []
            
            # Clean up any non-dict entries from transformations
            cleaned_transformations = []
            for trans in transformations:
                if isinstance(trans, dict):
                    cleaned_transformations.append(trans)
                else:
                    print(f"Warning: Skipping invalid transformation entry: {type(trans)} - {trans}")
            transformations = cleaned_transformations
            
            # Find and update the specific transformation
            for i, trans in enumerate(transformations):
                if isinstance(trans, dict) and trans.get('row_index') == row_index:
                    transformations[i] = transformation
                    break
            else:
                # Add new transformation if not found
                transformations.append(transformation)
            
            return self.store_column_transformations(column_name, transformations)
        except Exception as e:
            print(f"Error updating transformation: {e}")
            return False

class RedisColumnConfigManager:
    """Redis-based column configuration management"""
    
    def __init__(self, job_id: str):
        self.job_id = job_id
        self.redis = redis_jobs
        self.ttl = redis_config.ttl_config
    
    def get_all_columns(self) -> Dict[str, Any]:
        """Get all column configurations"""
        key = f'job:{self.job_id}:columns'
        data = self.redis.hgetall(key)
        
        if not data:
            return {}
        
        # Parse JSON values
        columns = {}
        for col_name, config_json in data.items():
            if isinstance(col_name, bytes):
                col_name = col_name.decode('utf-8')
            if isinstance(config_json, bytes):
                config_json = config_json.decode('utf-8')
            
            try:
                columns[col_name] = json.loads(config_json)
            except:
                columns[col_name] = {}
        
        return columns
    
    def update_column_config(self, column_name: str, config: Dict[str, Any]) -> bool:
        """Update configuration for a specific column"""
        try:
            key = f'job:{self.job_id}:columns'
            self.redis.hset(key, column_name, json.dumps(config))
            self.redis.expire(key, self.ttl['job_metadata'])
            return True
        except Exception as e:
            print(f"Error updating column config: {e}")
            return False
    
    def get_column_config(self, column_name: str) -> Dict[str, Any]:
        """Get configuration for a specific column"""
        key = f'job:{self.job_id}:columns'
        config_json = self.redis.hget(key, column_name)
        
        if not config_json:
            return {}
        
        try:
            if isinstance(config_json, bytes):
                config_json = config_json.decode('utf-8')
            return json.loads(config_json)
        except Exception as e:
            print(f"Error getting column config: {e}")
            return {}

class RedisLLMCacheManager:
    """Redis-based LLM cache manager (replaces PostgreSQL)"""
    
    def __init__(self):
        self.redis = redis_llm
        self.ttl = redis_config.ttl_config
    
    def _generate_cache_key(self, model_name: str, system_prompt: str, user_prompt: str, model_settings: Dict[str, Any]) -> str:
        """Generate cache key for LLM request"""
        cache_data = {
            'model_name': model_name,
            'system_prompt': system_prompt or '',
            'user_prompt': user_prompt,
            'model_settings': model_settings
        }
        
        cache_string = json.dumps(cache_data, sort_keys=True, ensure_ascii=False)
        return hashlib.sha256(cache_string.encode('utf-8')).hexdigest()
    
    def get_cached_response(self, model_name: str, system_prompt: str, user_prompt: str, model_settings: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Get cached LLM response"""
        cache_key = self._generate_cache_key(model_name, system_prompt, user_prompt, model_settings)
        
        cached_data = self.redis.get(f'llm:cache:{cache_key}')
        if not cached_data:
            # Update cache miss stats
            self._update_stats(cache_hit=False)
            return None
        
        try:
            result = json.loads(cached_data)
            
            # Update access count and stats
            result['access_count'] = result.get('access_count', 0) + 1
            self.redis.set(f'llm:cache:{cache_key}', json.dumps(result))
            self.redis.expire(f'llm:cache:{cache_key}', self.ttl['llm_cache'])
            
            self._update_stats(cache_hit=True)
            
            return {
                'response': result['response'],
                'metadata': result.get('metadata', {}),
                'cached_at': result.get('created_at'),
                'access_count': result['access_count'],
                'from_cache': True
            }
        except Exception as e:
            print(f"Error retrieving cached response: {e}")
            return None
    
    def cache_response(self, model_name: str, system_prompt: str, user_prompt: str, 
                      model_settings: Dict[str, Any], response: str, 
                      response_metadata: Optional[Dict[str, Any]] = None,
                      ttl_hours: Optional[int] = None) -> bool:
        """Cache LLM response"""
        try:
            cache_key = self._generate_cache_key(model_name, system_prompt, user_prompt, model_settings)
            
            cache_data = {
                'response': response,
                'model_name': model_name,
                'created_at': datetime.utcnow().isoformat() + 'Z',
                'access_count': 1,
                'metadata': response_metadata or {}
            }
            
            # Store cache entry
            self.redis.set(f'llm:cache:{cache_key}', json.dumps(cache_data))
            
            # Set TTL
            ttl_seconds = (ttl_hours * 3600) if ttl_hours else self.ttl['llm_cache']
            self.redis.expire(f'llm:cache:{cache_key}', ttl_seconds)
            
            # Add to cache index for cleanup
            self.redis.zadd('llm:cache:index', {cache_key: datetime.utcnow().timestamp()})
            
            return True
        except Exception as e:
            print(f"Error caching response: {e}")
            return False
    
    def _update_stats(self, cache_hit: bool):
        """Update daily cache statistics"""
        today = datetime.utcnow().strftime('%Y-%m-%d')
        stats_key = f'llm:stats:daily:{today}'
        
        field = 'cache_hits' if cache_hit else 'cache_misses'
        self.redis.hincrby(stats_key, field, 1)
        self.redis.expire(stats_key, 86400 * 30)  # Keep stats for 30 days
    
    def get_cache_stats(self, days: int = 7) -> Dict[str, Any]:
        """Get comprehensive cache statistics"""
        stats = {
            'daily_stats': [],
            'summary': {
                'total_requests': 0,
                'cache_hits': 0,
                'cache_misses': 0,
                'hit_rate_percent': 0,
                'total_cache_entries': 0,
                'active_entries': 0,
                'cache_size_mb': 0
            }
        }
        
        total_hits = 0
        total_misses = 0
        
        # Get daily stats
        for i in range(days):
            date = (datetime.utcnow() - timedelta(days=i)).strftime('%Y-%m-%d')
            day_stats = self.redis.hgetall(f'llm:stats:daily:{date}')
            
            hits = int(day_stats.get(b'cache_hits', 0)) if day_stats else 0
            misses = int(day_stats.get(b'cache_misses', 0)) if day_stats else 0
            
            stats['daily_stats'].append({
                'date': date,
                'cache_hits': hits,
                'cache_misses': misses
            })
            
            total_hits += hits
            total_misses += misses
        
        # Calculate basic stats
        total_requests = total_hits + total_misses
        hit_rate = (total_hits / total_requests * 100) if total_requests > 0 else 0
        
        # Get current cache info
        try:
            # Count total cache entries (excluding index)
            cache_keys = self.redis.keys('llm:cache:*')
            cache_keys = [k for k in cache_keys if not k.decode('utf-8').endswith(':index')]
            total_cache_entries = len(cache_keys)
            
            # Count active entries (those with TTL > 0)
            active_entries = 0
            total_size_bytes = 0
            
            for key in cache_keys[:100]:  # Sample first 100 to avoid performance issues
                try:
                    ttl = self.redis.ttl(key)
                    if ttl > 0:  # Key has TTL set and is active
                        active_entries += 1
                    
                    # Estimate size of cache entry
                    data = self.redis.get(key)
                    if data:
                        total_size_bytes += len(data)
                except Exception:
                    continue
            
            # Estimate total size based on sample
            if len(cache_keys) > 100:
                avg_size = total_size_bytes / min(len(cache_keys), 100)
                estimated_total_size = avg_size * total_cache_entries
                active_ratio = active_entries / min(len(cache_keys), 100)
                estimated_active_entries = int(total_cache_entries * active_ratio)
            else:
                estimated_total_size = total_size_bytes
                estimated_active_entries = active_entries
            
            cache_size_mb = round(estimated_total_size / (1024 * 1024), 2)
            
        except Exception as e:
            print(f"Error getting cache info: {e}")
            total_cache_entries = 0
            estimated_active_entries = 0
            cache_size_mb = 0
        
        stats['summary'] = {
            'total_requests': total_requests,
            'cache_hits': total_hits,
            'cache_misses': total_misses,
            'hit_rate_percent': round(hit_rate, 2),
            'total_cache_entries': total_cache_entries,
            'active_entries': estimated_active_entries,
            'cache_size_mb': cache_size_mb
        }
        
        return stats
    
    def clear_cache(self, pattern: str = '*') -> int:
        """Clear cache entries by pattern"""
        keys = self.redis.keys(f'llm:cache:{pattern}')
        if keys:
            return self.redis.delete(*keys)
        return 0

class RedisProgressManager:
    """Redis-based progress tracking for background tasks"""
    
    def __init__(self, job_id: str):
        self.job_id = job_id
        self.redis = redis_sessions
        self.ttl = redis_config.ttl_config
    
    def update_progress(self, status: str, percent: int, current_operation: str = '', 
                       total_items: int = 0, processed_items: int = 0, eta_seconds: int = 0) -> bool:
        """Update progress information"""
        try:
            progress_data = {
                'status': status,
                'percent': percent,
                'current_operation': current_operation,
                'total_items': total_items,
                'processed_items': processed_items,
                'eta_seconds': eta_seconds,
                'updated_at': datetime.utcnow().isoformat() + 'Z'
            }
            
            key = f'progress:{self.job_id}'
            self.redis.set(key, json.dumps(progress_data))
            self.redis.expire(key, self.ttl['progress_updates'])
            
            return True
        except Exception as e:
            print(f"Error updating progress: {e}")
            return False
    
    def get_progress(self) -> Dict[str, Any]:
        """Get current progress information"""
        key = f'progress:{self.job_id}'
        progress_data = self.redis.get(key)
        
        if not progress_data:
            return {
                'status': 'unknown',
                'percent': 0,
                'current_operation': '',
                'total_items': 0,
                'processed_items': 0,
                'eta_seconds': 0
            }
        
        try:
            return json.loads(progress_data)
        except Exception as e:
            print(f"Error getting progress: {e}")
            return {'status': 'error', 'percent': 0} 