from flask import Blueprint, jsonify, request, render_template, current_app, make_response
from werkzeug.utils import secure_filename
from datetime import datetime
import json
import os
import pandas as pd
import time
from modules.auth import requires_auth
# Redis-based JobManager is accessed via current_app.redis_job_manager
from models.redis_models import RedisJobManager, RedisJobDataManager

import_jobs_bp = Blueprint('import_jobs_bp', __name__)

@import_jobs_bp.route('/import-jobs')
@requires_auth
def import_jobs_page_route(): # Renamed to avoid conflict if any other import_jobs_page exists
    """Render the page that lists all import jobs."""
    return render_template('import_jobs.html')

@import_jobs_bp.route('/api/import/jobs', methods=['POST'])
@requires_auth
def create_import_job_route(): # Renamed
    redis_job_manager = current_app.redis_job_manager
    data = request.get_json(silent=True) or {}
    job_name = data.get('job_name', f'New Job - {datetime.utcnow().strftime("%Y%m%d-%H%M%S")}')
    original_filename = data.get('original_filename')
    
    try:
        job_id = redis_job_manager.create_job(job_name, original_filename)
        current_app.logger.info(f"Created new Redis job: {job_id}")
        return jsonify({"success": True, "job_id": job_id, "message": "New import job created."}), 201
    except Exception as e:
        current_app.logger.error(f"Failed to create Redis job: {str(e)}", exc_info=True)
        return jsonify({"success": False, "message": "Failed to create new import job."}), 500

@import_jobs_bp.route('/api/import/jobs', methods=['GET'])
@requires_auth
def list_import_jobs_route(): # Renamed
    redis_job_manager = current_app.redis_job_manager
    try:
        # Get pagination parameters
        page = int(request.args.get('page', 1))
        limit = int(request.args.get('limit', 50))
        offset = (page - 1) * limit
        
        jobs = redis_job_manager.list_jobs(limit=limit, offset=offset)
        
        # Convert Redis job format to match old format for compatibility
        jobs_summary = []
        for job in jobs:
            jobs_summary.append({
                "job_id": job.get('id'),
                "job_name": job.get('name'),
                "status": job.get('status'),
                "original_filename": job.get('original_filename', 'N/A'),
                "created_at": job.get('created_at'),
                "last_modified_at": job.get('updated_at')
            })
        
        return jsonify(jobs_summary)
    except Exception as e:
        current_app.logger.error(f"Failed to list Redis jobs: {str(e)}", exc_info=True)
        return jsonify([])

@import_jobs_bp.route('/api/import/jobs/<job_id>', methods=['GET'])
@requires_auth
def get_import_job_route(job_id): # Renamed
    redis_job_manager = current_app.redis_job_manager
    try:
        job_data = redis_job_manager.get_job_metadata(job_id)
        if job_data:
            # Get additional data from Redis
            mapping_definitions = redis_job_manager.get_mapping_definitions(job_id)
            source_columns = redis_job_manager.get_source_columns(job_id)
            validation_configs = redis_job_manager.get_validation_configs(job_id)
            
            # Load actual data for AI mapping and frontend functionality
            from models.redis_models import RedisJobDataManager
            data_manager = RedisJobDataManager(job_id)
            
            # Load data for frontend display - load more rows for better UX
            original_data = []
            row_count = job_data.get('row_count', 0)
            if row_count > 0:
                # For small datasets (<=500 rows), load all data
                # For larger datasets, load first 500 rows for preview
                max_rows_to_load = min(500, row_count)
                pages_to_load = (max_rows_to_load // 20) + 1  # 20 is the page size

                current_app.logger.info(f"Loading {max_rows_to_load} rows from {row_count} total rows for job {job_id}")

                for page in range(1, pages_to_load + 1):
                    page_data = data_manager.get_data_page(page, 20)
                    if page_data.get('rows'):
                        # Remove the _row_index field that was added during storage
                        clean_rows = []
                        for row in page_data['rows']:
                            clean_row = {k: v for k, v in row.items() if k != '_row_index'}
                            clean_rows.append(clean_row)
                        original_data.extend(clean_rows)
                        if len(original_data) >= max_rows_to_load:
                            break

                # Trim to exact number if we loaded too many
                original_data = original_data[:max_rows_to_load]
                current_app.logger.info(f"Successfully loaded {len(original_data)} rows for frontend display")
            
            # Load column configurations from Redis
            column_mappings = {}
            column_logic = {}
            column_string_values = {}
            column_llm_settings = {}
            
            # Load column_mappings
            field_key = f'job:{job_id}:column_mappings'
            field_data = redis_job_manager.redis.get(field_key)
            current_app.logger.info(f"📂 Loading column_mappings for job {job_id}: {'found' if field_data else 'not found'}")
            if field_data:
                try:
                    column_mappings = json.loads(field_data)
                    current_app.logger.info(f"✅ Loaded column_mappings: {len(column_mappings)} items")
                except json.JSONDecodeError:
                    current_app.logger.warning(f"Failed to parse column_mappings for job {job_id}")
                    column_mappings = {}
            
            # Load column_logic
            field_key = f'job:{job_id}:column_logic'
            field_data = redis_job_manager.redis.get(field_key)
            current_app.logger.info(f"📂 Loading column_logic for job {job_id}: {'found' if field_data else 'not found'}")
            if field_data:
                try:
                    column_logic = json.loads(field_data)
                    current_app.logger.info(f"✅ Loaded column_logic: {len(column_logic)} items")
                except json.JSONDecodeError:
                    current_app.logger.warning(f"Failed to parse column_logic for job {job_id}")
                    column_logic = {}
            
            # Load column_string_values
            field_key = f'job:{job_id}:column_string_values'
            field_data = redis_job_manager.redis.get(field_key)
            current_app.logger.info(f"📂 Loading column_string_values for job {job_id}: {'found' if field_data else 'not found'}")
            if field_data:
                try:
                    column_string_values = json.loads(field_data)
                    current_app.logger.info(f"✅ Loaded column_string_values: {len(column_string_values)} items")
                except json.JSONDecodeError:
                    current_app.logger.warning(f"Failed to parse column_string_values for job {job_id}")
                    column_string_values = {}
            
            # Load column_llm_settings
            field_key = f'job:{job_id}:column_llm_settings'
            field_data = redis_job_manager.redis.get(field_key)
            current_app.logger.info(f"📂 Loading column_llm_settings for job {job_id}: {'found' if field_data else 'not found'}")
            if field_data:
                try:
                    column_llm_settings = json.loads(field_data)
                    current_app.logger.info(f"✅ Loaded column_llm_settings: {len(column_llm_settings)} items")
                except json.JSONDecodeError:
                    current_app.logger.warning(f"Failed to parse column_llm_settings for job {job_id}")
                    column_llm_settings = {}
            
            # Load UI state data
            ui_key = f'job:{job_id}:ui_state'
            ui_state = redis_job_manager.redis.get(ui_key)
            user_notes = ""
            deactivated_fields = []
            ui_column_order = []
            
            if ui_state:
                try:
                    ui_data = json.loads(ui_state)
                    user_notes = ui_data.get('user_notes', '')
                    deactivated_fields = ui_data.get('deactivated_fields', [])
                    ui_column_order = ui_data.get('ui_column_order', [])
                except json.JSONDecodeError:
                    current_app.logger.warning(f"Failed to parse UI state for job {job_id}")
            
            # For compatibility with old format, convert Redis format
            compatible_job_data = {
                "job_id": job_data.get('id'),
                "job_name": job_data.get('name'),
                "status": job_data.get('status'),
                "original_filename": job_data.get('original_filename'),
                "created_at": job_data.get('created_at'),
                "last_modified_at": job_data.get('updated_at'),
                "row_count": job_data.get('row_count', 0),
                "column_count": job_data.get('column_count', 0),
                # Add actual data from Redis storage
                "original_data": original_data,
                "source_columns": source_columns,
                "mapping_definitions": mapping_definitions,
                "validation_configs": validation_configs,
                # Load actual column configurations from Redis
                "column_mappings": column_mappings,
                "column_logic": column_logic,
                "column_string_values": column_string_values,
                "column_llm_settings": column_llm_settings,
                "user_notes": user_notes,
                "mapped_data": [],
                "deactivated_fields": deactivated_fields,
                "ui_column_order": ui_column_order
            }
            return jsonify(compatible_job_data)
        return jsonify({"success": False, "message": "Job not found."}), 404
    except Exception as e:
        current_app.logger.error(f"Failed to get Redis job {job_id}: {str(e)}", exc_info=True)
        return jsonify({"success": False, "message": "Job not found."}), 404

@import_jobs_bp.route('/api/import/jobs/<job_id>', methods=['PUT'])
@requires_auth
def update_import_job_route(job_id): # Renamed
    redis_job_manager = current_app.redis_job_manager
    current_job_data = redis_job_manager.get_job_metadata(job_id)
    if not current_job_data:
        return jsonify({"success": False, "message": "Job not found to update."}), 404
    
    try:
        update_data = request.get_json()
        if not update_data:
            return jsonify({"success": False, "message": "No update data provided."}), 400
        
        # Handle granular updates for different data types
        results = {}
        
        # Update metadata if present
        metadata_updates = {}
        allowed_fields = ['name', 'status', 'original_filename']
        
        for key, value in update_data.items():
            if key == 'job_name':
                metadata_updates['name'] = value
            elif key in allowed_fields:
                metadata_updates[key] = value
        
        if metadata_updates:
            if redis_job_manager.update_job_metadata(job_id, metadata_updates):
                results['metadata'] = 'updated'
            else:
                results['metadata'] = 'failed'
        
        # Update column configurations if present - store directly in Redis
        config_fields = ['column_mappings', 'column_logic', 'column_string_values', 'column_llm_settings']
        for field in config_fields:
            if field in update_data:
                # Store the entire field data structure directly
                field_key = f'job:{job_id}:{field}'
                field_data = update_data[field]
                
                current_app.logger.info(f"💾 Saving {field} for job {job_id}: {len(field_data)} items")
                redis_job_manager.redis.set(field_key, json.dumps(field_data))
                redis_job_manager.redis.expire(field_key, redis_job_manager.ttl['job_metadata'])
                results[field] = 'updated'
        
        # Store UI state data if present
        ui_fields = ['deactivated_fields', 'ui_column_order', 'user_notes']
        ui_updates = {}
        for field in ui_fields:
            if field in update_data:
                ui_updates[field] = update_data[field]
        
        if ui_updates:
            ui_key = f'job:{job_id}:ui_state'
            current_ui_state = redis_job_manager.redis.get(ui_key)
            if current_ui_state:
                current_ui_state = json.loads(current_ui_state)
            else:
                current_ui_state = {}
            
            current_ui_state.update(ui_updates)
            redis_job_manager.redis.set(ui_key, json.dumps(current_ui_state))
            redis_job_manager.redis.expire(ui_key, redis_job_manager.ttl['ui_state'])
            results['ui_state'] = 'updated'
        
        if results:
            return jsonify({"success": True, "job_id": job_id, "message": "Job updated successfully.", "updates": results})
        else:
            return jsonify({"success": False, "message": "No valid update data provided."}), 400
            
    except Exception as e:
        current_app.logger.error(f"Error updating job {job_id} in route: {str(e)}", exc_info=True)
        return jsonify({"success": False, "message": f"Error processing update: {str(e)}"}), 500

# New granular endpoints for efficient data updates
@import_jobs_bp.route('/api/import/jobs/<job_id>/column-config', methods=['PUT'])
@requires_auth
def update_job_column_config(job_id):
    """Update column configuration for a specific column"""
    try:
        update_data = request.get_json()
        if not update_data or 'column_name' not in update_data:
            return jsonify({"success": False, "message": "Column name required"}), 400
        
        from models.redis_models import RedisColumnConfigManager
        column_manager = RedisColumnConfigManager(job_id)
        
        column_name = update_data['column_name']
        config = update_data.get('config', {})
        
        if column_manager.update_column_config(column_name, config):
            return jsonify({"success": True, "message": f"Column {column_name} config updated"})
        else:
            return jsonify({"success": False, "message": "Failed to update column config"}), 500
            
    except Exception as e:
        current_app.logger.error(f"Error updating column config for job {job_id}: {str(e)}", exc_info=True)
        return jsonify({"success": False, "message": f"Error: {str(e)}"}), 500

@import_jobs_bp.route('/api/import/jobs/<job_id>/ui-state', methods=['PUT'])
@requires_auth
def update_job_ui_state(job_id):
    """Update UI state (deactivated fields, column order, notes)"""
    try:
        redis_job_manager = current_app.redis_job_manager
        
        # Check if job exists
        if not redis_job_manager.get_job_metadata(job_id):
            return jsonify({"success": False, "message": "Job not found"}), 404
        
        update_data = request.get_json()
        if not update_data:
            return jsonify({"success": False, "message": "No update data provided"}), 400
        
        ui_key = f'job:{job_id}:ui_state'
        current_ui_state = redis_job_manager.redis.get(ui_key)
        if current_ui_state:
            current_ui_state = json.loads(current_ui_state)
        else:
            current_ui_state = {}
        
        # Update with new data
        current_ui_state.update(update_data)
        
        redis_job_manager.redis.set(ui_key, json.dumps(current_ui_state))
        redis_job_manager.redis.expire(ui_key, redis_job_manager.ttl['ui_state'])
        
        return jsonify({"success": True, "message": "UI state updated successfully"})
        
    except Exception as e:
        current_app.logger.error(f"Error updating UI state for job {job_id}: {str(e)}", exc_info=True)
        return jsonify({"success": False, "message": f"Error: {str(e)}"}), 500

# New pagination endpoints for efficient data loading
@import_jobs_bp.route('/api/import/jobs/<job_id>/data', methods=['GET'])
@requires_auth
def get_job_data_paginated(job_id):
    """Get paginated job data for efficient loading"""
    try:
        redis_job_manager = current_app.redis_job_manager
        
        # Check if job exists
        job_metadata = redis_job_manager.get_job_metadata(job_id)
        if not job_metadata:
            return jsonify({"success": False, "message": "Job not found"}), 404
        
        # Get pagination parameters
        page = int(request.args.get('page', 1))
        page_size = int(request.args.get('page_size', 20))
        
        # Limit page size to reasonable bounds
        page_size = min(max(page_size, 1), 100)
        
        from models.redis_models import RedisJobDataManager
        data_manager = RedisJobDataManager(job_id)
        
        # Get the paginated data
        page_data = data_manager.get_data_page(page, page_size)
        
        if page_data:
            return jsonify({
                "success": True,
                "data": page_data.get('rows', []),
                "page": page,
                "page_size": page_size,
                "total_rows": job_metadata.get('row_count', 0),
                "total_pages": page_data.get('total_pages', 0),
                "has_next": page_data.get('has_next', False),
                "has_prev": page_data.get('has_prev', False)
            })
        else:
            return jsonify({
                "success": True,
                "data": [],
                "page": page,
                "page_size": page_size,
                "total_rows": 0,
                "total_pages": 0,
                "has_next": False,
                "has_prev": False
            })
            
    except Exception as e:
        current_app.logger.error(f"Error getting paginated data for job {job_id}: {str(e)}", exc_info=True)
        return jsonify({"success": False, "message": f"Error: {str(e)}"}), 500

@import_jobs_bp.route('/api/import/jobs/<job_id>/data/search', methods=['POST'])
@requires_auth
def search_job_data(job_id):
    """Search and filter job data with pagination"""
    try:
        redis_job_manager = current_app.redis_job_manager
        
        # Check if job exists
        job_metadata = redis_job_manager.get_job_metadata(job_id)
        if not job_metadata:
            return jsonify({"success": False, "message": "Job not found"}), 404
        
        request_data = request.get_json()
        
        # Get search parameters
        search_term = request_data.get('search_term', '')
        search_columns = request_data.get('search_columns', [])
        filters = request_data.get('filters', {})
        page = int(request_data.get('page', 1))
        page_size = int(request_data.get('page_size', 20))
        
        # Limit page size to reasonable bounds
        page_size = min(max(page_size, 1), 100)
        
        from models.redis_models import RedisJobDataManager
        data_manager = RedisJobDataManager(job_id)
        
        # Get all data for filtering (in production, this would be optimized)
        all_data = []
        total_rows = job_metadata.get('row_count', 0)
        if total_rows > 0:
            pages_needed = (total_rows // 20) + 1
            for data_page in range(1, pages_needed + 1):
                page_data = data_manager.get_data_page(data_page, 20)
                if page_data.get('rows'):
                    all_data.extend(page_data['rows'])
        
        # Apply search and filters
        filtered_data = []
        
        for row in all_data:
            # Apply search term filter
            if search_term:
                match_found = False
                if search_columns:
                    # Search in specific columns
                    for col in search_columns:
                        if col in row and search_term.lower() in str(row[col]).lower():
                            match_found = True
                            break
                else:
                    # Search in all columns
                    for value in row.values():
                        if search_term.lower() in str(value).lower():
                            match_found = True
                            break
                
                if not match_found:
                    continue
            
            # Apply column filters
            filter_passed = True
            for col, filter_value in filters.items():
                if col in row:
                    if isinstance(filter_value, list):
                        # Multiple value filter
                        if row[col] not in filter_value:
                            filter_passed = False
                            break
                    else:
                        # Single value filter
                        if str(row[col]).lower() != str(filter_value).lower():
                            filter_passed = False
                            break
            
            if filter_passed:
                filtered_data.append(row)
        
        # Apply pagination to filtered results
        total_filtered = len(filtered_data)
        start_idx = (page - 1) * page_size
        end_idx = start_idx + page_size
        page_data = filtered_data[start_idx:end_idx]
        
        total_pages = (total_filtered + page_size - 1) // page_size
        
        return jsonify({
            "success": True,
            "data": page_data,
            "page": page,
            "page_size": page_size,
            "total_rows": total_filtered,
            "total_pages": total_pages,
            "has_next": page < total_pages,
            "has_prev": page > 1,
            "search_applied": bool(search_term or filters)
        })
            
    except Exception as e:
        current_app.logger.error(f"Error searching job data for job {job_id}: {str(e)}", exc_info=True)
        return jsonify({"success": False, "message": f"Error: {str(e)}"}), 500

@import_jobs_bp.route('/api/import/jobs/<job_id>', methods=['DELETE'])
@requires_auth
def delete_import_job_route(job_id): # Renamed
    redis_job_manager = current_app.redis_job_manager
    try:
        # Check existence first for a more specific error message if it was already gone
        existing_job = redis_job_manager.get_job_metadata(job_id)
        if not existing_job:
            return jsonify({"success": False, "message": "Job not found or already deleted."}), 404

        if redis_job_manager.delete_job(job_id):
            current_app.logger.info(f"Deleted Redis job: {job_id}")
            return jsonify({"success": True, "message": f"Job {job_id} deleted successfully."})
        else:
            return jsonify({"success": False, "message": "Failed to delete job."}), 500
    except Exception as e:
        current_app.logger.error(f"Failed to delete Redis job {job_id}: {str(e)}", exc_info=True)
        return jsonify({"success": False, "message": "Failed to delete job."}), 500

@import_jobs_bp.route('/api/import/jobs/<job_id>/rename', methods=['POST'])
@requires_auth
def rename_import_job_route(job_id): # Renamed
    redis_job_manager = current_app.redis_job_manager
    job_data = redis_job_manager.get_job_metadata(job_id)
    if not job_data:
        return jsonify({"success": False, "message": "Job not found."}), 404

    try:
        request_payload = request.get_json(silent=True)
        if not request_payload:
            current_app.logger.error(f"No JSON payload received for job rename: {job_id}")
            return jsonify({"success": False, "message": "No JSON data received. Please check your request format."}), 400
            
        new_job_name = request_payload.get('job_name')
        if not new_job_name or not isinstance(new_job_name, str) or len(new_job_name.strip()) == 0:
            current_app.logger.error(f"Invalid job name received: {new_job_name}")
            return jsonify({"success": False, "message": "New job name is required and must be a non-empty string."}), 400
        
        metadata_updates = {'name': new_job_name.strip()}
        if redis_job_manager.update_job_metadata(job_id, metadata_updates):
            current_app.logger.info(f"Job {job_id} successfully renamed to '{new_job_name.strip()}'")
            return jsonify({"success": True, "message": f"Job {job_id} renamed to '{new_job_name.strip()}'."})
        else:
            current_app.logger.error(f"Failed to save job data after rename for job {job_id}")
            return jsonify({"success": False, "message": "Failed to save renamed job."}), 500
    except Exception as e:
        current_app.logger.error(f"Error renaming job {job_id} in route: {str(e)}", exc_info=True)
        return jsonify({"success": False, "message": f"Error processing rename request: {str(e)}"}), 500

@import_jobs_bp.route('/api/import/jobs/<job_id>/export-excel', methods=['GET'])
@requires_auth
def export_job_excel_by_id(job_id):
    """Export specific job as Excel file with data and configuration worksheets - OPTIMIZED."""
    try:
        start_time = time.time()
        redis_job_manager = current_app.redis_job_manager

        # Get job metadata and data from Redis
        job_metadata = redis_job_manager.get_job_metadata(job_id)
        if not job_metadata:
            return jsonify({'success': False, 'message': 'Job not found'}), 404

        # PERFORMANCE OPTIMIZATION: Use larger page sizes for export
        from models.redis_models import RedisJobDataManager
        data_manager = RedisJobDataManager(job_id)

        # Load all data for export with optimized pagination
        original_data = []
        row_count = int(job_metadata.get('row_count', 0))

        if row_count > 0:
            # Use larger page size for export (100 rows per page instead of 20)
            export_page_size = 100
            pages_needed = (row_count + export_page_size - 1) // export_page_size

            current_app.logger.info(f"📊 Exporting {row_count} rows in {pages_needed} pages (page size: {export_page_size})")

            for page in range(1, pages_needed + 1):
                page_data = data_manager.get_data_page(page, export_page_size)
                if page_data.get('rows'):
                    original_data.extend(page_data['rows'])

            current_app.logger.info(f"✅ Loaded {len(original_data)} rows for export")
        
        # Reconstruct job data structure
        job_data = {
            'job_id': job_metadata.get('id'),
            'job_name': job_metadata.get('name'),
            'status': job_metadata.get('status'),
            'original_filename': job_metadata.get('original_filename'),
            'created_at': job_metadata.get('created_at'),
            'last_modified_at': job_metadata.get('updated_at'),
            'original_data': original_data,
            'source_columns': redis_job_manager.get_source_columns(job_id),
            'mapping_definitions': redis_job_manager.get_mapping_definitions(job_id),
            'validation_configs': redis_job_manager.get_validation_configs(job_id),
            # Add empty defaults for compatibility
            'column_mappings': {},
            'column_logic': {},
            'column_string_values': {},
            'mapped_data': [],
            'transformed_data': []
        }
            
        # PERFORMANCE OPTIMIZATION: Use optimized Excel export
        from modules.utils.excel_export_import import export_job_to_excel_optimized

        # Try optimized export first, fallback to standard if not available
        try:
            excel_buffer = export_job_to_excel_optimized(job_data)
        except (AttributeError, ImportError):
            # Fallback to standard export
            from modules.utils.excel_export_import import export_job_to_excel
            excel_buffer = export_job_to_excel(job_data)

        # Create filename with timestamp
        job_name = job_metadata.get('name', 'import_job')
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        # Clean filename for filesystem
        safe_filename = "".join(c for c in job_name if c.isalnum() or c in (' ', '-', '_')).rstrip()
        filename = f"{safe_filename}_{job_id}_{timestamp}.xlsx"

        export_time = time.time() - start_time
        current_app.logger.info(f"🚀 Excel export completed in {export_time:.2f}s for job {job_id}")

        response = make_response(excel_buffer.getvalue())
        response.headers['Content-Type'] = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        response.headers['Content-Disposition'] = f'attachment; filename="{filename}"'

        return response
        
    except Exception as e:
        current_app.logger.error(f"Error exporting job {job_id} to Excel: {str(e)}", exc_info=True)
        return jsonify({'success': False, 'message': f'Export failed: {str(e)}'}), 500

@import_jobs_bp.route('/api/import/jobs/<job_id>/import-excel', methods=['POST'])
@requires_auth
def import_job_excel_by_id(job_id):
    """Import Excel file into specific job, replacing data and optionally configuration."""
    try:
        redis_job_manager = current_app.redis_job_manager
        
        if 'file' not in request.files:
            return jsonify({'success': False, 'message': 'No file provided'}), 400
        
        file = request.files['file']
        if file.filename == '':
            return jsonify({'success': False, 'message': 'No file selected'}), 400
            
        job_metadata = redis_job_manager.get_job_metadata(job_id)
        if not job_metadata:
            return jsonify({'success': False, 'message': 'Job not found'}), 404
            
        # Import from Excel file (this function expects the old format, so we reconstruct it)
        from modules.utils.excel_export_import import import_job_from_excel
        
        # Create minimal job data structure for import function compatibility
        job_data = {
            'job_id': job_id,
            'job_name': job_metadata.get('name'),
            'status': job_metadata.get('status')
        }
        
        imported_data = import_job_from_excel(file, job_data)
        
        # Store imported data in Redis
        from models.redis_models import RedisJobDataManager
        data_manager = RedisJobDataManager(job_id)
        
        # Store the imported original data
        if imported_data.get('original_data'):
            data_stored = data_manager.store_source_data(imported_data['original_data'])
            if not data_stored:
                return jsonify({'success': False, 'message': 'Failed to store imported data in Redis'}), 500
        
        # Update job metadata
        updates = {
            'name': imported_data.get('job_name', job_metadata.get('name')),
            'status': 'data-uploaded',
            'row_count': len(imported_data.get('original_data', [])),
            'column_count': len(imported_data.get('source_columns', []))
        }
        redis_job_manager.update_job_metadata(job_id, updates)
        
        # Store additional configurations if present
        if imported_data.get('mapping_definitions'):
            redis_job_manager.store_mapping_definitions(job_id, imported_data['mapping_definitions'])
        
        if imported_data.get('source_columns'):
            redis_job_manager.store_source_columns(job_id, imported_data['source_columns'])
        
        return jsonify({
            'success': True, 
            'job_id': job_id,
            'message': 'Excel file imported successfully',
            'config_imported': imported_data.get('config_imported', False),
            'data_rows': len(imported_data.get('original_data', []))
        })
            
    except Exception as e:
        current_app.logger.error(f"Error importing Excel file to job {job_id}: {str(e)}", exc_info=True)
        return jsonify({'success': False, 'message': f'Import failed: {str(e)}'}), 500

@import_jobs_bp.route('/api/import/jobs/import-excel-new', methods=['POST'])
@requires_auth
def import_job_excel_new():
    """Create a new job from Excel file with data and optionally configuration."""
    try:
        redis_job_manager = current_app.redis_job_manager
        
        if 'file' not in request.files:
            return jsonify({'success': False, 'message': 'No file provided'}), 400
        
        file = request.files['file']
        if file.filename == '':
            return jsonify({'success': False, 'message': 'No file selected'}), 400
            
        # Create new job
        job_name_suggestion = request.form.get('job_name') or f"Imported from {file.filename}"
        job_id = redis_job_manager.create_job(job_name_suggestion, file.filename)
        
        # Create minimal job data structure for import function compatibility
        new_job_data = {
            'job_id': job_id,
            'job_name': job_name_suggestion,
            'status': 'draft'
        }
        
        # Import from Excel file
        from modules.utils.excel_export_import import import_job_from_excel
        imported_data = import_job_from_excel(file, new_job_data)
        
        # Store imported data in Redis
        from models.redis_models import RedisJobDataManager
        data_manager = RedisJobDataManager(job_id)
        
        # Store the imported original data
        if imported_data.get('original_data'):
            data_stored = data_manager.store_source_data(imported_data['original_data'])
            if not data_stored:
                return jsonify({'success': False, 'message': 'Failed to store imported data in Redis'}), 500
        
        # Update job metadata
        updates = {
            'name': imported_data.get('job_name', job_name_suggestion),
            'status': 'data-uploaded',
            'row_count': len(imported_data.get('original_data', [])),
            'column_count': len(imported_data.get('source_columns', []))
        }
        redis_job_manager.update_job_metadata(job_id, updates)
        
        # Store additional configurations if present
        if imported_data.get('mapping_definitions'):
            redis_job_manager.store_mapping_definitions(job_id, imported_data['mapping_definitions'])
        
        if imported_data.get('source_columns'):
            redis_job_manager.store_source_columns(job_id, imported_data['source_columns'])
        
        return jsonify({
            'success': True, 
            'job_id': job_id,
            'message': 'New job created from Excel file successfully',
            'config_imported': imported_data.get('config_imported', False),
            'data_rows': len(imported_data.get('original_data', []))
        })
            
    except Exception as e:
        current_app.logger.error(f"Error creating job from Excel file: {str(e)}", exc_info=True)
        return jsonify({'success': False, 'message': f'Import failed: {str(e)}'}), 500

@import_jobs_bp.route('/api/import/jobs/<job_id>/import-json', methods=['POST'])
@requires_auth
def import_job_json_by_id(job_id):
    """Import JSON file into specific job, replacing all data."""
    try:
        redis_job_manager = current_app.redis_job_manager
        
        if 'file' not in request.files:
            return jsonify({'success': False, 'message': 'No file provided'}), 400
        
        file = request.files['file']
        if file.filename == '':
            return jsonify({'success': False, 'message': 'No file selected'}), 400
            
        # Check if it's a JSON file
        filename = secure_filename(file.filename)
        file_ext = os.path.splitext(filename)[1].lower()
        
        if file_ext != '.json':
            return jsonify({'success': False, 'message': 'Only JSON files are supported for import'}), 400
            
        current_job_metadata = redis_job_manager.get_job_metadata(job_id)
        if not current_job_metadata:
            return jsonify({'success': False, 'message': 'Job not found'}), 404
            
        try:
            # Read and parse JSON file
            file_content = file.read().decode('utf-8')
            imported_job_data = json.loads(file_content)
            
            # Validate that it's a proper job data structure
            required_fields = ['job_id', 'job_name', 'status']
            for field in required_fields:
                if field not in imported_job_data:
                    return jsonify({'success': False, 'message': f'Invalid job file: missing required field "{field}"'}), 400
            
            # Store imported data in Redis
            from models.redis_models import RedisJobDataManager
            data_manager = RedisJobDataManager(job_id)
            
            # Store original data if present
            if imported_job_data.get('original_data'):
                data_stored = data_manager.store_source_data(imported_job_data['original_data'])
                if not data_stored:
                    return jsonify({'success': False, 'message': 'Failed to store imported data in Redis'}), 500
            
            # Update job metadata
            updates = {
                'name': imported_job_data.get('job_name', current_job_metadata.get('name')),
                'status': 'data-uploaded',
                'row_count': len(imported_job_data.get('original_data', [])),
                'column_count': len(imported_job_data.get('source_columns', []))
            }
            redis_job_manager.update_job_metadata(job_id, updates)
            
            # Store additional configurations
            if imported_job_data.get('mapping_definitions'):
                redis_job_manager.store_mapping_definitions(job_id, imported_job_data['mapping_definitions'])
            
            if imported_job_data.get('source_columns'):
                redis_job_manager.store_source_columns(job_id, imported_job_data['source_columns'])
            
            if imported_job_data.get('validation_configs'):
                redis_job_manager.store_validation_configs(job_id, imported_job_data['validation_configs'])
            
            return jsonify({
                'success': True, 
                'job_id': job_id,
                'message': f'JSON job file imported successfully from {filename}',
                'original_job_id': imported_job_data.get('job_id'),
                'data_rows': len(imported_job_data.get('original_data', [])),
                'transformed_data_rows': len(imported_job_data.get('transformed_data', [])),
                'mapping_definitions': len(imported_job_data.get('mapping_definitions', []))
            })
                
        except json.JSONDecodeError as e:
            return jsonify({'success': False, 'message': f'Invalid JSON file: {str(e)}'}), 400
        except UnicodeDecodeError as e:
            return jsonify({'success': False, 'message': f'File encoding error: {str(e)}'}), 400
            
    except Exception as e:
        current_app.logger.error(f"Error importing JSON file to job {job_id}: {str(e)}", exc_info=True)
        return jsonify({'success': False, 'message': f'Import failed: {str(e)}'}), 500

@import_jobs_bp.route('/api/import/jobs/import-json-new', methods=['POST'])
@requires_auth
def import_job_json_new():
    """Create a new job from JSON file."""
    try:
        redis_job_manager = current_app.redis_job_manager
        
        if 'file' not in request.files:
            return jsonify({'success': False, 'message': 'No file provided'}), 400
        
        file = request.files['file']
        if file.filename == '':
            return jsonify({'success': False, 'message': 'No file selected'}), 400
            
        # Check if it's a JSON file
        filename = secure_filename(file.filename)
        file_ext = os.path.splitext(filename)[1].lower()
        
        if file_ext != '.json':
            return jsonify({'success': False, 'message': 'Only JSON files are supported for import'}), 400
            
        try:
            # Read and parse JSON file
            file_content = file.read().decode('utf-8')
            imported_job_data = json.loads(file_content)
            
            # Validate that it's a proper job data structure
            required_fields = ['job_id', 'job_name', 'status']
            for field in required_fields:
                if field not in imported_job_data:
                    return jsonify({'success': False, 'message': f'Invalid job file: missing required field "{field}"'}), 400
            
            # Use provided job name or derive from filename
            job_name_suggestion = request.form.get('job_name') 
            if not job_name_suggestion:
                job_name_suggestion = imported_job_data.get('job_name', f"Imported from {filename}")
            
            # Create new job
            new_job_id = redis_job_manager.create_job(job_name_suggestion, filename)
            
            # Store imported data in Redis
            from models.redis_models import RedisJobDataManager
            data_manager = RedisJobDataManager(new_job_id)
            
            # Store original data if present
            if imported_job_data.get('original_data'):
                data_stored = data_manager.store_source_data(imported_job_data['original_data'])
                if not data_stored:
                    return jsonify({'success': False, 'message': 'Failed to store imported data in Redis'}), 500
            
            # Update job metadata
            updates = {
                'name': job_name_suggestion,
                'status': 'data-uploaded',
                'row_count': len(imported_job_data.get('original_data', [])),
                'column_count': len(imported_job_data.get('source_columns', []))
            }
            redis_job_manager.update_job_metadata(new_job_id, updates)
            
            # Store additional configurations
            if imported_job_data.get('mapping_definitions'):
                redis_job_manager.store_mapping_definitions(new_job_id, imported_job_data['mapping_definitions'])
            
            if imported_job_data.get('source_columns'):
                redis_job_manager.store_source_columns(new_job_id, imported_job_data['source_columns'])
            
            if imported_job_data.get('validation_configs'):
                redis_job_manager.store_validation_configs(new_job_id, imported_job_data['validation_configs'])
            
            return jsonify({
                'success': True, 
                'job_id': new_job_id,
                'message': f'New job created successfully from {filename}',
                'original_job_id': imported_job_data.get('job_id'),
                'data_rows': len(imported_job_data.get('original_data', [])),
                'transformed_data_rows': len(imported_job_data.get('transformed_data', [])),
                'mapping_definitions': len(imported_job_data.get('mapping_definitions', []))
            })
                
        except json.JSONDecodeError as e:
            return jsonify({'success': False, 'message': f'Invalid JSON file: {str(e)}'}), 400
        except UnicodeDecodeError as e:
            return jsonify({'success': False, 'message': f'File encoding error: {str(e)}'}), 400
            
    except Exception as e:
        current_app.logger.error(f"Error creating job from JSON file: {str(e)}", exc_info=True)
        return jsonify({'success': False, 'message': f'Import failed: {str(e)}'}), 500

@import_jobs_bp.route('/api/import/jobs/<job_id>/export-json', methods=['GET'])
@requires_auth
def export_job_json_by_id(job_id):
    """Export specific job as JSON file with complete job data."""
    try:
        redis_job_manager = current_app.redis_job_manager
        
        # Get job metadata and data from Redis
        job_metadata = redis_job_manager.get_job_metadata(job_id)
        if not job_metadata:
            return jsonify({'success': False, 'message': 'Job not found'}), 404
        
        # Reconstruct complete job data for export
        from models.redis_models import RedisJobDataManager
        data_manager = RedisJobDataManager(job_id)
        
        # Load all data for export
        original_data = []
        row_count = job_metadata.get('row_count', 0)
        if row_count > 0:
            pages_needed = (row_count // 20) + 1
            for page in range(1, pages_needed + 1):
                page_data = data_manager.get_data_page(page, 20)
                if page_data.get('rows'):
                    original_data.extend(page_data['rows'])
        
        # Reconstruct complete job data structure
        job_data = {
            'job_id': job_metadata.get('id'),
            'job_name': job_metadata.get('name'),
            'status': job_metadata.get('status'),
            'original_filename': job_metadata.get('original_filename'),
            'created_at': job_metadata.get('created_at'),
            'last_modified_at': job_metadata.get('updated_at'),
            'original_data': original_data,
            'source_columns': redis_job_manager.get_source_columns(job_id),
            'mapping_definitions': redis_job_manager.get_mapping_definitions(job_id),
            'validation_configs': redis_job_manager.get_validation_configs(job_id),
            # Add empty defaults for compatibility
            'column_mappings': {},
            'column_logic': {},
            'column_string_values': {},
            'mapped_data': [],
            'transformed_data': []
        }
            
        # Create filename
        job_name = job_metadata.get('name', 'import_job')
        # Clean filename for filesystem
        safe_filename = "".join(c for c in job_name if c.isalnum() or c in (' ', '-', '_')).rstrip()
        filename = f"jobs_{safe_filename}_{job_id}.json"
        
        # Convert job data to JSON
        json_content = json.dumps(job_data, indent=2, ensure_ascii=False)
        
        response = make_response(json_content)
        response.headers['Content-Type'] = 'application/json'
        response.headers['Content-Disposition'] = f'attachment; filename="{filename}"'
        
        return response
        
    except Exception as e:
        current_app.logger.error(f"Error exporting job {job_id} to JSON: {str(e)}", exc_info=True)
        return jsonify({'success': False, 'message': f'Export failed: {str(e)}'}), 500

# Redis-optimized bulk processing endpoint
@import_jobs_bp.route('/api/import/jobs/<job_id>/bulk-apply-logic', methods=['POST'])
@requires_auth
def redis_bulk_apply_logic(job_id):
    """Redis-optimized bulk logic application with incremental storage"""
    try:
        request_data = request.get_json()
        if not request_data: 
            return jsonify({'success': False, 'message': 'No data'}), 400
        
        logic = request_data.get('logic', '')
        target_column_name = request_data.get('column_name', '')
        batch_size = min(int(request_data.get('batch_size', 50)), 100)  # Increased batch size for better performance
        page = int(request_data.get('page', 1))
        max_total_rows = request_data.get('max_total_rows', None)
        model_name = request_data.get('model_name', None)
        references = request_data.get('references', {})
        validation_config = request_data.get('validation_config', None)
        
        if not logic or not target_column_name:
            return jsonify({'success': False, 'message': 'Logic and column name are required'}), 400
        
        # Check if job exists using Redis
        redis_job_manager = current_app.redis_job_manager
        job_metadata = redis_job_manager.get_job_metadata(job_id)
        if not job_metadata:
            return jsonify({'success': False, 'message': f'Job {job_id} not found'}), 404
        
        # Get data manager for efficient data access
        from models.redis_models import RedisJobDataManager, RedisTransformationManager
        data_manager = RedisJobDataManager(job_id)
        transform_manager = RedisTransformationManager(job_id)
        
        # Get the current page of data to process
        page_data = data_manager.get_data_page(page, batch_size)
        
        current_app.logger.info(f"Processing page {page} with batch_size {batch_size} for job {job_id}")
        
        if not page_data or not page_data.get('rows'):
            return jsonify({
                'success': True,
                'message': 'No more data to process',
                'results': [],
                'progress': 100,
                'done': True,
                'page': page,
                'total_pages': 0
            })
        
        rows_to_process = page_data['rows']
        total_rows = job_metadata.get('row_count', 0)
        
        # Set up references for logic processing
        references = clean_data_recursively(references)
        references['target_column'] = target_column_name
        
        # Add validation data if needed
        if validation_config and 'worksheet' in validation_config and 'column' in validation_config:
            validation_configs = redis_job_manager.get_validation_configs(job_id)
            if target_column_name in validation_configs:
                from modules.google_sheets_service import google_sheets_service
                if google_sheets_service.is_authenticated():
                    validation_data = google_sheets_service.get_validation_data(
                        validation_config['worksheet'], 
                        validation_config['column']
                    )
                    references['validation_data'] = validation_data
        
        # Import the logic transformation function
        from modules.utils.llm_helpers import apply_logic_transformation
        
        # Process the batch
        current_app.logger.info(f"Processing page {page} with {len(rows_to_process)} rows for column '{target_column_name}'")
        
        try:
            results = apply_logic_transformation(
                logic,
                rows_to_process,
                references,
                model_name
            )
        except Exception as e:
            current_app.logger.error(f"Error in logic transformation: {str(e)}")
            return jsonify({'success': False, 'message': f'Logic processing error: {str(e)}'}), 500
        
        # Store results incrementally in Redis
        transformations = []
        formatted_results = []
        
        current_app.logger.info(f"Processing {len(rows_to_process)} rows with {len(results)} results")
        
        for i, (row, result) in enumerate(zip(rows_to_process, results)):
            row_index = (page - 1) * batch_size + i
            
            # Update the cell directly in Redis
            data_manager.update_cell(row_index, target_column_name, result)
            
            # Track the transformation - handle both dict and non-dict rows
            if isinstance(row, dict):
                original_value = row.get(target_column_name)
            else:
                current_app.logger.warning(f"Row {i} is not a dict, type: {type(row)}, value: {row}")
                original_value = None
            
            transformation_entry = {
                'row_index': row_index,
                'original_value': original_value,
                'transformed_value': result,
                'manual_edit': False,
                'logic_applied': logic[:100] + '...' if len(logic) > 100 else logic,
                'model_used': model_name,
                'timestamp': datetime.now().isoformat()
            }
            transformations.append(transformation_entry)
            
            # Format for response
            is_error = isinstance(result, str) and result.startswith('Error:')
            formatted_results.append({
                'row_index': row_index,
                'value': result,
                'isError': is_error
            })
        
        # Store transformation history for this column
        existing_transformations = transform_manager.get_column_transformations(target_column_name)
        
        # Ensure existing_transformations is a list of dictionaries
        if not isinstance(existing_transformations, list):
            current_app.logger.warning(f"Invalid transformations data type: {type(existing_transformations)}, resetting to empty list")
            existing_transformations = []
        
        # Clean up any non-dict entries from existing transformations
        cleaned_transformations = []
        for existing_trans in existing_transformations:
            if isinstance(existing_trans, dict):
                cleaned_transformations.append(existing_trans)
            else:
                current_app.logger.warning(f"Skipping invalid transformation entry: {type(existing_trans)} - {existing_trans}")
        existing_transformations = cleaned_transformations
        
        # Update existing transformations with new results
        for new_trans in transformations:
            updated = False
            for i, existing_trans in enumerate(existing_transformations):
                if isinstance(existing_trans, dict) and existing_trans.get('row_index') == new_trans['row_index']:
                    existing_transformations[i] = new_trans
                    updated = True
                    break
            if not updated:
                existing_transformations.append(new_trans)
        
        # Store updated transformations
        transform_manager.store_column_transformations(target_column_name, existing_transformations)
        
        # Calculate progress based on the actual processed rows count, not all transformations
        # For bulk runs, count only transformations that were created by logic processing (not manual edits)
        actual_processed_this_batch = len(formatted_results)
        
        # If this is a limited run (max_total_rows specified), calculate progress differently
        if max_total_rows:
            processed_count = len([t for t in existing_transformations if not t.get('manual_edit', False)])
            progress = min(100, (processed_count / max_total_rows) * 100)
            done = processed_count >= max_total_rows
            total_pages = 1  # For limited runs, treat as single conceptual "page"
        else:
            # For unlimited runs, calculate based on total data
            processed_count = len([t for t in existing_transformations if not t.get('manual_edit', False)])
            progress = (processed_count / total_rows) * 100 if total_rows > 0 else 100
            
            # Check if we're done - get total pages from pagination info
            pagination_info = page_data.get('pagination', {})
            total_pages = pagination_info.get('total', 0) // batch_size + (1 if pagination_info.get('total', 0) % batch_size > 0 else 0)
            if total_pages == 0:
                total_pages = 1
                
            done = page >= total_pages
        
        response_data = {
            'success': True,
            'message': f'Successfully processed {actual_processed_this_batch} rows',
            'results': formatted_results,
            'progress': progress,
            'page': page,
            'total_pages': total_pages,
            'done': done,
            'total_rows': max_total_rows if max_total_rows else total_rows,
            'processed_count': processed_count,
            'next_page': page + 1 if not done else None
        }
        
        current_app.logger.info(f"Redis bulk processing completed for page {page}. Progress: {progress:.1f}%, processed: {actual_processed_this_batch}, done: {done}")
        return jsonify(response_data)

    except Exception as e:
        current_app.logger.error(f"Error in Redis bulk apply logic: {str(e)}", exc_info=True)
        return jsonify({'success': False, 'message': f'Error: {str(e)}'}), 500

@import_jobs_bp.route('/api/import/jobs/<job_id>/high-performance-bulk-apply-logic', methods=['POST'])
@requires_auth
def high_performance_bulk_apply_logic(job_id):
    """
    High-performance bulk logic application using async processing and optimized batching.
    Designed for maximum speed and efficiency with large datasets.
    """
    try:
        request_data = request.get_json()
        if not request_data:
            return jsonify({'success': False, 'message': 'No data provided'}), 400

        logic = request_data.get('logic', '')
        target_column_name = request_data.get('column_name', '')
        model_name = request_data.get('model_name', None)
        references = request_data.get('references', {})

        if not logic or not target_column_name:
            return jsonify({'success': False, 'message': 'Logic and column name are required'}), 400

        current_app.logger.info(f"Starting high-performance processing for job {job_id}, column '{target_column_name}'")

        # Import the performance-optimized processor
        from modules.utils.performance_optimized_llm import PerformanceOptimizedLLMProcessor, run_async_processing

        # Create processor instance
        processor = PerformanceOptimizedLLMProcessor(job_id)

        # Define progress callback
        processed_batches = 0
        total_rows_processed = 0

        async def progress_callback(batch_id, rows_in_batch, processing_time):
            nonlocal processed_batches, total_rows_processed
            processed_batches += 1
            total_rows_processed += rows_in_batch
            current_app.logger.info(f"Completed batch {batch_id}: {rows_in_batch} rows in {processing_time:.2f}s")

        # Run async processing
        start_time = time.time()
        result = run_async_processing(
            processor,
            logic,
            target_column_name,
            references,
            model_name,
            progress_callback
        )
        total_time = time.time() - start_time

        if result['success']:
            current_app.logger.info(f"High-performance processing completed: {result['total_processed']} rows in {total_time:.2f}s ({result['rows_per_second']} rows/sec)")

            return jsonify({
                'success': True,
                'message': f'Successfully processed {result["total_processed"]} rows using high-performance mode',
                'total_processed': result['total_processed'],
                'processing_time': total_time,
                'rows_per_second': result['rows_per_second'],
                'performance_metrics': result['performance_metrics'],
                'batches_processed': result['batches_processed'],
                'errors': result['total_errors']
            })
        else:
            return jsonify({
                'success': False,
                'message': f'Processing completed with {result["total_errors"]} errors',
                'total_processed': result['total_processed'],
                'errors': result['total_errors']
            }), 500

    except Exception as e:
        current_app.logger.error(f"Error in high-performance bulk apply logic: {str(e)}")
        return jsonify({'success': False, 'message': f'Error: {str(e)}'}), 500

@import_jobs_bp.route('/api/import/jobs/<job_id>/performance-metrics', methods=['GET'])
@requires_auth
def get_performance_metrics(job_id):
    """Get performance metrics and statistics for a job"""
    try:
        from modules.utils.performance_monitor import PerformanceMonitor

        monitor = PerformanceMonitor(job_id)
        days = int(request.args.get('days', 7))

        # Get comprehensive performance summary
        summary = monitor.get_performance_summary(days)

        return jsonify({
            'success': True,
            'metrics': summary
        })

    except Exception as e:
        current_app.logger.error(f"Error getting performance metrics: {str(e)}")
        return jsonify({'success': False, 'message': f'Error: {str(e)}'}), 500

@import_jobs_bp.route('/api/import/jobs/<job_id>/real-time-metrics', methods=['GET'])
@requires_auth
def get_real_time_metrics(job_id):
    """Get real-time performance metrics for a job"""
    try:
        from modules.utils.performance_monitor import PerformanceMonitor

        monitor = PerformanceMonitor(job_id)
        metrics = monitor.get_real_time_metrics()

        return jsonify({
            'success': True,
            'metrics': metrics
        })

    except Exception as e:
        current_app.logger.error(f"Error getting real-time metrics: {str(e)}")
        return jsonify({'success': False, 'message': f'Error: {str(e)}'}), 500


def clean_data_recursively(data):
    """Clean data recursively to ensure JSON serialization compatibility"""
    if isinstance(data, dict):
        return {k: clean_data_recursively(v) for k, v in data.items()}
    elif isinstance(data, list):
        return [clean_data_recursively(item) for item in data]
    elif isinstance(data, (pd.Timestamp, datetime)):
        return data.isoformat() if hasattr(data, 'isoformat') else str(data)
    elif pd.isna(data) if pd else data != data:  # Handle NaN
        return None
    else:
        return data 