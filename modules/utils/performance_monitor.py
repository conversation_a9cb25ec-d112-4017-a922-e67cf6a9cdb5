"""
Performance monitoring and metrics collection for Akeneo import tool.
Tracks processing speeds, cache hit rates, and system performance.
"""

import time
import json
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta
from dataclasses import dataclass, asdict
from flask import current_app

from models.redis_models import RedisLLMCacheManager
from config.redis_config import redis_sessions


@dataclass
class PerformanceMetrics:
    """Data class for performance metrics"""
    operation_type: str
    start_time: float
    end_time: float
    rows_processed: int
    batch_size: int
    model_used: str
    cache_hit_rate: float
    errors_count: int
    memory_usage_mb: float
    
    @property
    def duration_seconds(self) -> float:
        return self.end_time - self.start_time
    
    @property
    def rows_per_second(self) -> float:
        return self.rows_processed / self.duration_seconds if self.duration_seconds > 0 else 0
    
    @property
    def success_rate(self) -> float:
        total_operations = self.rows_processed + self.errors_count
        return (self.rows_processed / total_operations * 100) if total_operations > 0 else 0


class PerformanceMonitor:
    """Performance monitoring and metrics collection"""
    
    def __init__(self, job_id: str):
        self.job_id = job_id
        self.redis = redis_sessions
        self.cache_manager = RedisLLMCacheManager()
        self.metrics_key = f"performance:{job_id}"
        
    def start_operation(self, operation_type: str, batch_size: int = 0, model_name: str = "") -> str:
        """Start tracking a performance operation"""
        operation_id = f"{operation_type}_{int(time.time() * 1000)}"
        
        operation_data = {
            "operation_id": operation_id,
            "operation_type": operation_type,
            "start_time": time.time(),
            "batch_size": batch_size,
            "model_name": model_name,
            "status": "running"
        }
        
        # Store operation data
        self.redis.hset(f"{self.metrics_key}:operations", operation_id, json.dumps(operation_data))
        self.redis.expire(f"{self.metrics_key}:operations", 86400)  # 24 hours
        
        return operation_id
    
    def end_operation(self, operation_id: str, rows_processed: int = 0, errors_count: int = 0) -> PerformanceMetrics:
        """End tracking a performance operation and calculate metrics"""
        
        # Get operation data
        operation_data_json = self.redis.hget(f"{self.metrics_key}:operations", operation_id)
        if not operation_data_json:
            raise ValueError(f"Operation {operation_id} not found")
        
        operation_data = json.loads(operation_data_json)
        end_time = time.time()
        
        # Get cache hit rate
        cache_stats = self.cache_manager.get_cache_stats(days=1)
        cache_hit_rate = cache_stats['summary']['hit_rate_percent']
        
        # Get memory usage (simplified)
        memory_usage_mb = self._estimate_memory_usage()
        
        # Create metrics object
        metrics = PerformanceMetrics(
            operation_type=operation_data["operation_type"],
            start_time=operation_data["start_time"],
            end_time=end_time,
            rows_processed=rows_processed,
            batch_size=operation_data["batch_size"],
            model_used=operation_data["model_name"],
            cache_hit_rate=cache_hit_rate,
            errors_count=errors_count,
            memory_usage_mb=memory_usage_mb
        )
        
        # Store metrics
        self._store_metrics(metrics)
        
        # Update operation status
        operation_data.update({
            "end_time": end_time,
            "status": "completed",
            "rows_processed": rows_processed,
            "errors_count": errors_count,
            "duration": metrics.duration_seconds,
            "rows_per_second": metrics.rows_per_second
        })
        
        self.redis.hset(f"{self.metrics_key}:operations", operation_id, json.dumps(operation_data))
        
        return metrics
    
    def _store_metrics(self, metrics: PerformanceMetrics):
        """Store performance metrics in Redis"""
        timestamp = datetime.utcnow().isoformat()
        metrics_data = asdict(metrics)
        metrics_data["timestamp"] = timestamp
        
        # Store in time-series format
        self.redis.zadd(f"{self.metrics_key}:timeseries", {json.dumps(metrics_data): time.time()})
        self.redis.expire(f"{self.metrics_key}:timeseries", 86400 * 7)  # Keep for 7 days
        
        # Store daily aggregates
        today = datetime.utcnow().strftime('%Y-%m-%d')
        daily_key = f"{self.metrics_key}:daily:{today}"
        
        # Update daily counters
        self.redis.hincrby(daily_key, "total_rows_processed", metrics.rows_processed)
        self.redis.hincrby(daily_key, "total_operations", 1)
        self.redis.hincrby(daily_key, "total_errors", metrics.errors_count)
        
        # Update daily averages (using running average)
        current_avg_rps = float(self.redis.hget(daily_key, "avg_rows_per_second") or 0)
        current_count = int(self.redis.hget(daily_key, "total_operations") or 1)
        new_avg_rps = ((current_avg_rps * (current_count - 1)) + metrics.rows_per_second) / current_count
        
        self.redis.hset(daily_key, "avg_rows_per_second", str(new_avg_rps))
        self.redis.hset(daily_key, "last_updated", timestamp)
        self.redis.expire(daily_key, 86400 * 30)  # Keep for 30 days
    
    def _estimate_memory_usage(self) -> float:
        """Estimate current memory usage (simplified)"""
        try:
            import psutil
            process = psutil.Process()
            return process.memory_info().rss / 1024 / 1024  # Convert to MB
        except ImportError:
            return 0.0  # psutil not available
    
    def get_performance_summary(self, days: int = 7) -> Dict[str, Any]:
        """Get comprehensive performance summary"""
        
        summary = {
            "job_id": self.job_id,
            "period_days": days,
            "daily_stats": [],
            "overall_stats": {
                "total_rows_processed": 0,
                "total_operations": 0,
                "total_errors": 0,
                "avg_rows_per_second": 0,
                "success_rate": 0,
                "cache_performance": {}
            },
            "recent_operations": [],
            "performance_trends": {}
        }
        
        # Get daily stats
        total_rows = 0
        total_ops = 0
        total_errors = 0
        total_rps = 0
        
        for i in range(days):
            date = (datetime.utcnow() - timedelta(days=i)).strftime('%Y-%m-%d')
            daily_key = f"{self.metrics_key}:daily:{date}"
            daily_data = self.redis.hgetall(daily_key)
            
            if daily_data:
                rows = int(daily_data.get(b'total_rows_processed', 0))
                ops = int(daily_data.get(b'total_operations', 0))
                errors = int(daily_data.get(b'total_errors', 0))
                avg_rps = float(daily_data.get(b'avg_rows_per_second', 0))
                
                summary["daily_stats"].append({
                    "date": date,
                    "rows_processed": rows,
                    "operations": ops,
                    "errors": errors,
                    "avg_rows_per_second": round(avg_rps, 2),
                    "success_rate": round((rows / (rows + errors) * 100) if (rows + errors) > 0 else 0, 2)
                })
                
                total_rows += rows
                total_ops += ops
                total_errors += errors
                total_rps += avg_rps
        
        # Calculate overall stats
        summary["overall_stats"] = {
            "total_rows_processed": total_rows,
            "total_operations": total_ops,
            "total_errors": total_errors,
            "avg_rows_per_second": round(total_rps / days if days > 0 else 0, 2),
            "success_rate": round((total_rows / (total_rows + total_errors) * 100) if (total_rows + total_errors) > 0 else 0, 2),
            "cache_performance": self.cache_manager.get_cache_stats(days)
        }
        
        # Get recent operations
        operations_data = self.redis.hgetall(f"{self.metrics_key}:operations")
        recent_ops = []
        
        for op_id, op_data_json in operations_data.items():
            if isinstance(op_id, bytes):
                op_id = op_id.decode('utf-8')
            if isinstance(op_data_json, bytes):
                op_data_json = op_data_json.decode('utf-8')
            
            try:
                op_data = json.loads(op_data_json)
                recent_ops.append(op_data)
            except json.JSONDecodeError:
                continue
        
        # Sort by start time and take last 10
        recent_ops.sort(key=lambda x: x.get('start_time', 0), reverse=True)
        summary["recent_operations"] = recent_ops[:10]
        
        return summary
    
    def get_real_time_metrics(self) -> Dict[str, Any]:
        """Get real-time performance metrics"""
        
        # Get current running operations
        operations_data = self.redis.hgetall(f"{self.metrics_key}:operations")
        running_ops = []
        
        for op_id, op_data_json in operations_data.items():
            if isinstance(op_data_json, bytes):
                op_data_json = op_data_json.decode('utf-8')
            
            try:
                op_data = json.loads(op_data_json)
                if op_data.get('status') == 'running':
                    # Calculate current duration
                    current_time = time.time()
                    duration = current_time - op_data['start_time']
                    op_data['current_duration'] = duration
                    running_ops.append(op_data)
            except json.JSONDecodeError:
                continue
        
        # Get cache stats
        cache_stats = self.cache_manager.get_cache_stats(days=1)
        
        return {
            "running_operations": running_ops,
            "cache_hit_rate": cache_stats['summary']['hit_rate_percent'],
            "memory_usage_mb": self._estimate_memory_usage(),
            "timestamp": datetime.utcnow().isoformat()
        }


def create_performance_report(job_id: str, days: int = 7) -> Dict[str, Any]:
    """Create a comprehensive performance report for a job"""
    monitor = PerformanceMonitor(job_id)
    return monitor.get_performance_summary(days)
