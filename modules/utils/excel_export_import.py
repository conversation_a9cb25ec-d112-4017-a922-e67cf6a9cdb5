import pandas as pd
import openpyxl
from openpyxl.utils.exceptions import InvalidFileException
from openpyxl.styles import Pat<PERSON><PERSON><PERSON>, <PERSON>ont, Alignment
import json
import io
from datetime import datetime

def export_job_to_excel(job_data):
    """
    Export job data to Excel file with two worksheets:
    1. 'Data' - contains the original_data
    2. 'Config' - contains mapping definitions, validation configs, and other settings
    """
    # Create a BytesIO buffer to store the Excel file
    excel_buffer = io.BytesIO()
    
    with pd.ExcelWriter(excel_buffer, engine='openpyxl') as writer:
        # Worksheet 1: Data
        original_data = job_data.get('original_data', [])
        if original_data:
            # Apply "- NOT NEEDED" logic to the data before export
            from modules.utils.product_family_utils import apply_not_needed_logic_to_data
            processed_data = apply_not_needed_logic_to_data(original_data)
            
            data_df = pd.DataFrame(processed_data)
            data_df.to_excel(writer, sheet_name='Data', index=False)
        else:
            # Create empty data sheet with a note
            empty_df = pd.DataFrame({'Note': ['No data available']})
            empty_df.to_excel(writer, sheet_name='Data', index=False)
        
        # Worksheet 2: Configuration
        config_data = {
            'job_name': job_data.get('job_name', ''),
            'original_filename': job_data.get('original_filename', ''),
            'status': job_data.get('status', ''),
            'created_at': job_data.get('created_at', ''),
            'last_modified': job_data.get('last_modified', datetime.now().isoformat()),
            'mapping_definitions': json.dumps(job_data.get('mapping_definitions', []), indent=2),
            'validation_configs': json.dumps(job_data.get('validation_configs', {}), indent=2),
            'source_columns': json.dumps(job_data.get('source_columns', []), indent=2),
            'mapped_data': json.dumps(job_data.get('mapped_data', [])[:5] if job_data.get('mapped_data') else [], indent=2),  # Only first 5 rows for preview
            'user_notes': job_data.get('user_notes', ''),
            'settings': json.dumps(job_data.get('settings', {}), indent=2)
        }
        
        # Convert config to DataFrame with key-value pairs
        config_df = pd.DataFrame([
            {'Setting': key, 'Value': value} 
            for key, value in config_data.items()
        ])
        config_df.to_excel(writer, sheet_name='Config', index=False)
        
        # Add metadata worksheet with additional info
        metadata = {
            'exported_at': datetime.now().isoformat(),
            'export_version': '1.0',
            'job_id': job_data.get('job_id', ''),
            'data_rows_count': len(original_data),
            'mapped_columns_count': len(job_data.get('mapping_definitions', [])),
            'validation_rules_count': len(job_data.get('validation_configs', {}))
        }
        
        metadata_df = pd.DataFrame([
            {'Metadata': key, 'Value': value} 
            for key, value in metadata.items()
        ])
        metadata_df.to_excel(writer, sheet_name='Metadata', index=False)
    
    excel_buffer.seek(0)
    return excel_buffer

def export_job_to_excel_optimized(job_data):
    """
    PERFORMANCE OPTIMIZED: Export job data to Excel file with enhanced performance.
    Uses optimized pandas operations and memory management.
    """
    import time
    start_time = time.time()

    # Create a BytesIO buffer to store the Excel file
    excel_buffer = io.BytesIO()

    # OPTIMIZATION: Use xlsxwriter engine for better performance with large datasets
    try:
        with pd.ExcelWriter(excel_buffer, engine='xlsxwriter', options={'strings_to_numbers': True}) as writer:
            # Worksheet 1: Data (OPTIMIZED)
            original_data = job_data.get('original_data', [])
            if original_data:
                print(f"📊 Processing {len(original_data)} rows for Excel export...")

                # OPTIMIZATION: Apply "- NOT NEEDED" logic efficiently
                from modules.utils.product_family_utils import apply_not_needed_logic_to_data
                processed_data = apply_not_needed_logic_to_data(original_data)

                # OPTIMIZATION: Create DataFrame with optimized dtypes
                data_df = pd.DataFrame(processed_data)

                # OPTIMIZATION: Optimize data types to reduce memory usage
                for col in data_df.columns:
                    if data_df[col].dtype == 'object':
                        # Try to convert to more efficient types
                        try:
                            # Check if it's numeric
                            pd.to_numeric(data_df[col], errors='raise')
                            data_df[col] = pd.to_numeric(data_df[col], errors='coerce')
                        except (ValueError, TypeError):
                            # Keep as string but optimize
                            data_df[col] = data_df[col].astype('string')

                # OPTIMIZATION: Write with optimized settings
                data_df.to_excel(writer, sheet_name='Data', index=False,
                               freeze_panes=(1, 0))  # Freeze header row

                # OPTIMIZATION: Apply formatting for better readability
                workbook = writer.book
                worksheet = writer.sheets['Data']

                # Header format
                header_format = workbook.add_format({
                    'bold': True,
                    'text_wrap': True,
                    'valign': 'top',
                    'fg_color': '#D7E4BC',
                    'border': 1
                })

                # Apply header formatting
                for col_num, value in enumerate(data_df.columns.values):
                    worksheet.write(0, col_num, value, header_format)
                    # Auto-adjust column width
                    worksheet.set_column(col_num, col_num, min(max(len(str(value)) + 2, 10), 50))

            else:
                # Create empty data sheet with a note
                empty_df = pd.DataFrame({'Note': ['No data available']})
                empty_df.to_excel(writer, sheet_name='Data', index=False)

            # Worksheet 2: Config (OPTIMIZED)
            config_data = []

            # OPTIMIZATION: Build config data more efficiently
            mapping_definitions = job_data.get('mapping_definitions', [])
            validation_configs = job_data.get('validation_configs', {})

            for mapping in mapping_definitions:
                config_row = {
                    'Column': mapping.get('column', ''),
                    'Description': mapping.get('description', ''),
                    'Required': mapping.get('required', False),
                    'Validation': json.dumps(validation_configs.get(mapping.get('column', ''), {})) if validation_configs.get(mapping.get('column', '')) else ''
                }
                config_data.append(config_row)

            if config_data:
                config_df = pd.DataFrame(config_data)
                config_df.to_excel(writer, sheet_name='Config', index=False)

                # Apply formatting to config sheet
                config_worksheet = writer.sheets['Config']
                for col_num, value in enumerate(config_df.columns.values):
                    config_worksheet.write(0, col_num, value, header_format)
                    config_worksheet.set_column(col_num, col_num, min(max(len(str(value)) + 2, 15), 60))

            # Worksheet 3: Metadata (NEW)
            metadata_data = {
                'Property': ['Job Name', 'Status', 'Original Filename', 'Row Count', 'Column Count', 'Created At', 'Last Modified'],
                'Value': [
                    job_data.get('job_name', 'N/A'),
                    job_data.get('status', 'N/A'),
                    job_data.get('original_filename', 'N/A'),
                    len(original_data),
                    len(original_data[0].keys()) if original_data else 0,
                    job_data.get('created_at', 'N/A'),
                    job_data.get('last_modified_at', 'N/A')
                ]
            }

            metadata_df = pd.DataFrame(metadata_data)
            metadata_df.to_excel(writer, sheet_name='Metadata', index=False)

            # Apply formatting to metadata sheet
            metadata_worksheet = writer.sheets['Metadata']
            for col_num, value in enumerate(metadata_df.columns.values):
                metadata_worksheet.write(0, col_num, value, header_format)
                metadata_worksheet.set_column(0, 0, 20)  # Property column
                metadata_worksheet.set_column(1, 1, 40)  # Value column

    except ImportError:
        # Fallback to openpyxl if xlsxwriter is not available
        print("⚠️ xlsxwriter not available, falling back to openpyxl")
        return export_job_to_excel(job_data)

    processing_time = time.time() - start_time
    print(f"✅ Optimized Excel export completed in {processing_time:.2f}s")

    excel_buffer.seek(0)
    return excel_buffer

def import_job_from_excel(file_storage_object, existing_job_data):
    """
    Import job data from Excel file.
    If Config worksheet exists, import configuration settings.
    Always import data from Data worksheet.
    Returns updated job_data with imported information.
    """
    try:
        # Read the Excel file
        excel_data = pd.ExcelFile(file_storage_object)
        worksheet_names = excel_data.sheet_names
        
        # Initialize result
        imported_data = existing_job_data.copy()
        imported_data['config_imported'] = False
        
        # Import data from 'Data' worksheet
        if 'Data' in worksheet_names:
            data_df = pd.read_excel(file_storage_object, sheet_name='Data')
            # Replace NaN values with None to make it JSON serializable
            data_df = data_df.astype(object).replace({pd.NA: None, float('nan'): None})
            imported_data['original_data'] = data_df.to_dict('records')
            imported_data['source_columns'] = list(data_df.columns)
            imported_data['original_filename'] = f"Imported from Excel: {getattr(file_storage_object, 'filename', 'unknown.xlsx')}"
        
        # Import configuration if 'Config' worksheet exists
        if 'Config' in worksheet_names:
            config_df = pd.read_excel(file_storage_object, sheet_name='Config')
            config_dict = dict(zip(config_df['Setting'], config_df['Value']))
            
            # Update job data with imported configuration
            if 'job_name' in config_dict and config_dict['job_name']:
                imported_data['job_name'] = config_dict['job_name']
            
            if 'mapping_definitions' in config_dict and config_dict['mapping_definitions']:
                try:
                    imported_data['mapping_definitions'] = json.loads(config_dict['mapping_definitions'])
                except (json.JSONDecodeError, TypeError):
                    pass  # Keep existing mapping_definitions if import fails
            
            if 'validation_configs' in config_dict and config_dict['validation_configs']:
                try:
                    imported_data['validation_configs'] = json.loads(config_dict['validation_configs'])
                except (json.JSONDecodeError, TypeError):
                    pass  # Keep existing validation_configs if import fails
            
            if 'user_notes' in config_dict and config_dict['user_notes']:
                imported_data['user_notes'] = config_dict['user_notes']
            
            if 'settings' in config_dict and config_dict['settings']:
                try:
                    imported_data['settings'] = json.loads(config_dict['settings'])
                except (json.JSONDecodeError, TypeError):
                    pass  # Keep existing settings if import fails
            
            imported_data['config_imported'] = True
        
        # Update status and timestamps
        imported_data['status'] = 'data-uploaded'
        imported_data['last_modified'] = datetime.now().isoformat()
        
        return imported_data
        
    except InvalidFileException:
        raise Exception("Invalid Excel file format")
    except Exception as e:
        raise Exception(f"Error reading Excel file: {str(e)}")

def get_excel_worksheet_info(file_storage_object):
    """
    Get information about worksheets in an Excel file.
    Returns dictionary with worksheet names and basic info.
    """
    try:
        excel_data = pd.ExcelFile(file_storage_object)
        worksheets = {}
        
        for sheet_name in excel_data.sheet_names:
            try:
                df = pd.read_excel(file_storage_object, sheet_name=sheet_name, nrows=0)  # Just get columns
                worksheets[sheet_name] = {
                    'columns': list(df.columns),
                    'column_count': len(df.columns)
                }
            except Exception:
                worksheets[sheet_name] = {
                    'columns': [],
                    'column_count': 0,
                    'error': 'Could not read worksheet'
                }
        
        return {
            'success': True,
            'worksheets': worksheets,
            'has_config': 'Config' in excel_data.sheet_names,
            'has_data': 'Data' in excel_data.sheet_names
        }
        
    except Exception as e:
        return {
            'success': False,
            'error': str(e)
        }

def create_supplier_excel_export(data, export_options, supplier_config=None):
    """
    Create enhanced Excel export with supplier-specific formatting and explanations.
    
    Args:
        data: List of data rows to export
        export_options: Dict with export configuration:
            - export_type: 'all_columns', 'mapped_only', 'supplier_relevant'
            - job_data: Optional job data for mapping information
        supplier_config: Dict with supplier configuration from Lieferantenfelder worksheet
        
    Returns:
        BytesIO buffer with Excel file
    """
    excel_buffer = io.BytesIO()
    
    if not data:
        # Create empty workbook with message
        wb = openpyxl.Workbook()
        ws = wb.active
        ws.title = "Export Data"
        ws['A1'] = "No data available for export"
        wb.save(excel_buffer)
        excel_buffer.seek(0)
        return excel_buffer
    
    # Apply "- NOT NEEDED" logic to the data before export
    from modules.utils.product_family_utils import apply_not_needed_logic_to_data
    processed_data = apply_not_needed_logic_to_data(data, supplier_config)
    
    # Create DataFrame from processed data
    df = pd.DataFrame(processed_data)
    
    # Apply export type filtering
    export_type = export_options.get('export_type', 'all_columns')
    job_data = export_options.get('job_data', {})
    
    if export_type == 'mapped_only':
        # Only include columns that have mappings
        mapped_columns = []
        mapping_definitions = job_data.get('mapping_definitions', [])
        for mapping in mapping_definitions:
            col_name = mapping.get('column', '')
            if col_name in df.columns:
                mapped_columns.append(col_name)
        
        if mapped_columns:
            df = df[mapped_columns]
    
    elif export_type == 'supplier_relevant':
        # Include mapped columns and exclude columns that suppliers don't need to fill
        # but keep our mapped and filled columns
        mapped_columns = []
        mapping_definitions = job_data.get('mapping_definitions', [])
        for mapping in mapping_definitions:
            col_name = mapping.get('column', '')
            if col_name in df.columns:
                mapped_columns.append(col_name)
        
        # Add any columns that have data (non-empty cells)
        filled_columns = []
        for col in df.columns:
            if not df[col].isna().all():  # Column has at least some data
                filled_columns.append(col)
        
        # Combine mapped and filled columns
        relevant_columns = list(set(mapped_columns + filled_columns))
        
        # Filter out columns that suppliers don't need based on config
        if supplier_config:
            final_columns = []
            for col in relevant_columns:
                # Check if this column needs supplier input based on config
                needs_supplier_input = False
                for family_code, attrs in supplier_config.items():
                    if col in attrs:
                        if attrs[col].get('pflege_bei_lieferant', False):
                            needs_supplier_input = True
                            break
                
                # Include if it needs supplier input OR it's already mapped/filled
                if needs_supplier_input or col in mapped_columns:
                    final_columns.append(col)
            
            df = df[final_columns] if final_columns else df
    
    # Create Excel file with styling
    with pd.ExcelWriter(excel_buffer, engine='openpyxl') as writer:
        df.to_excel(writer, sheet_name='Export Data', index=False, startrow=1)
        
        # Get workbook and worksheet for styling
        workbook = writer.book
        worksheet = writer.sheets['Export Data']
        
        # Add explanation row (row 2)
        if supplier_config:
            for col_idx, col_name in enumerate(df.columns, 1):
                explanation = ""
                # Find explanation from supplier config
                for family_code, attrs in supplier_config.items():
                    if col_name in attrs:
                        explanation = attrs[col_name].get('erklaerung_fuer_lieferant', '')
                        break
                
                if explanation:
                    cell = worksheet.cell(row=2, column=col_idx)
                    cell.value = explanation
                    # Style explanation row
                    cell.font = Font(italic=True, color="666666")
                    cell.alignment = Alignment(wrap_text=True)
        
        # Define colors for different cell types
        supplier_fill = PatternFill(start_color="FFEB9C", end_color="FFEB9C", fill_type="solid")  # Light yellow
        header_fill = PatternFill(start_color="4472C4", end_color="4472C4", fill_type="solid")  # Blue
        not_needed_fill = PatternFill(start_color="D3D3D3", end_color="D3D3D3", fill_type="solid")  # Light gray
        header_font = Font(color="FFFFFF", bold=True)
        not_needed_font = Font(italic=True, color="666666")
        
        # Style header row
        for col_idx in range(1, len(df.columns) + 1):
            cell = worksheet.cell(row=1, column=col_idx)
            cell.fill = header_fill
            cell.font = header_font
            cell.alignment = Alignment(horizontal="center")
        
        # Color cells based on their content and supplier requirements
        if supplier_config:
            for row_idx in range(3, len(df) + 3):  # Start from row 3 (after header and explanation)
                for col_idx, col_name in enumerate(df.columns, 1):
                    cell = worksheet.cell(row=row_idx, column=col_idx)
                    cell_value = str(cell.value) if cell.value is not None else ""
                    
                    # Style "- NOT NEEDED" cells
                    if cell_value == "- NOT NEEDED":
                        cell.fill = not_needed_fill
                        cell.font = not_needed_font
                        cell.alignment = Alignment(horizontal="center")
                    else:
                        # Check if this column needs supplier input
                        needs_supplier_input = False
                        for family_code, attrs in supplier_config.items():
                            if col_name in attrs:
                                if attrs[col_name].get('pflege_bei_lieferant', False):
                                    needs_supplier_input = True
                                    break
                        
                        # Color empty cells that need supplier input
                        if needs_supplier_input and (cell.value is None or str(cell.value).strip() == ""):
                            cell.fill = supplier_fill
        
        # Auto-adjust column widths
        for column in worksheet.columns:
            max_length = 0
            column_letter = column[0].column_letter
            for cell in column:
                try:
                    cell_length = len(str(cell.value)) if cell.value else 0
                    if cell_length > max_length:
                        max_length = cell_length
                except:
                    pass
            adjusted_width = min(max_length + 2, 50)  # Cap at 50 characters
            worksheet.column_dimensions[column_letter].width = adjusted_width
    
    excel_buffer.seek(0)
    return excel_buffer 