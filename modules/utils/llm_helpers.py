import os
import json
import pandas as pd
import requests # Needed for direct API calls
from flask import current_app # For logging
import re
from config.config import (
    DEFAULT_LLM_MODEL, 
    DEFAULT_PROMPT_TEMPLATE,
    DEFAULT_SYSTEM_PROMPT,
    CLASSIFICATION_SYSTEM_PROMPT,
    COLUMN_MAPPING_SYSTEM_PROMPT,
    DATA_TRANSFORMATION_SYSTEM_PROMPT
)
from modules.utils.llm_call_logger import log_llm_call, update_llm_call_response
from modules.utils.openrouter_service import openrouter_service
from modules.utils.model_selection_service import model_selection_service
from models.redis_models import RedisLLMCacheManager

def safe_str_for_logging(obj, max_length=300):
    """
    Safely convert any object to a truncated string for logging, handling Unicode properly.
    
    Args:
        obj: Object to convert to string
        max_length (int): Maximum length before truncation
    
    Returns:
        str: Safe string representation for logging
    """
    try:
        if isinstance(obj, str):
            # Ensure proper Unicode handling by explicitly encoding/decoding
            safe_str = obj.encode('utf-8', errors='replace').decode('utf-8')
        elif isinstance(obj, dict) or isinstance(obj, list):
            # Use json.dumps with ensure_ascii=False for proper Unicode handling
            safe_str = json.dumps(obj, ensure_ascii=False, indent=2)
        else:
            # Convert to string and ensure Unicode handling
            safe_str = str(obj).encode('utf-8', errors='replace').decode('utf-8')
        
        # Truncate if needed
        if len(safe_str) > max_length:
            return safe_str[:max_length] + "..."
        return safe_str
    except Exception as e:
        return f"[Error converting to string: {str(e)}]"

def make_cached_llm_call(system_prompt, user_prompt, model_name, call_type="generic", 
                        temperature=0.7, response_format=None, cache_ttl_hours=24, 
                        rate_limit_callback=None, **kwargs):
    """
    Make an LLM call with caching support.
    
    Args:
        system_prompt (str): The system prompt
        user_prompt (str): The user prompt  
        model_name (str): The model to use
        call_type (str): Type of call for logging (e.g., 'transformation', 'classification')
        temperature (float): Temperature setting
        response_format (dict): Response format (e.g., {"type": "json_object"})
        cache_ttl_hours (int): Cache time-to-live in hours (None for no expiry)
        rate_limit_callback (callable): Callback for rate limit delays
        **kwargs: Additional parameters for the LLM call
        
    Returns:
        dict: Response containing 'response', 'from_cache', and other metadata
    """
    # Prepare model settings for cache key
    model_settings = {
        'temperature': temperature,
        'response_format': response_format,
        **kwargs
    }
    
    # Check cache first
    redis_llm_cache = RedisLLMCacheManager()
    cached_result = redis_llm_cache.get_cached_response(
        model_name=model_name,
        system_prompt=system_prompt,
        user_prompt=user_prompt,
        model_settings=model_settings
    )
    
    if cached_result:
        current_app.logger.info(f"🎯 Cache HIT for {call_type} call (accessed {cached_result['access_count']} times)")
        return {
            'response': cached_result['response'],
            'from_cache': True,
            'cached_at': cached_result['cached_at'],
            'access_count': cached_result['access_count'],
            'metadata': cached_result.get('metadata', {})
        }
    
    # Cache miss - make actual LLM call
    current_app.logger.info(f"💾 Cache MISS for {call_type} call - calling LLM")
    
    # Log the call for detailed tracking
    call_metadata = {
        'call_type': call_type,
        'model_settings': model_settings,
        'from_cache': False
    }
    call_id = log_llm_call(call_type, model_name, system_prompt, user_prompt, metadata=call_metadata)
    
    try:
        # Make the API call via OpenRouter
        messages = [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": user_prompt}
        ]
        
        api_response = openrouter_service.make_chat_completion_with_retry(
            model=model_name,
            messages=messages,
            temperature=temperature,
            response_format=response_format,
            rate_limit_callback=rate_limit_callback,
            **kwargs
        )
        
        response_content = api_response["choices"][0]["message"]["content"]
        
        # Update the detailed log with response
        response_metadata = {
            'usage': api_response.get('usage', {}),
            'model': api_response.get('model', model_name),
            'finish_reason': api_response.get('choices', [{}])[0].get('finish_reason', '')
        }
        update_llm_call_response(call_id, response_content, response_metadata)
        
        # Cache the response
        cache_success = redis_llm_cache.cache_response(
            model_name=model_name,
            system_prompt=system_prompt,
            user_prompt=user_prompt,
            model_settings=model_settings,
            response=response_content,
            response_metadata=response_metadata,
            ttl_hours=cache_ttl_hours
        )
        
        if cache_success:
            current_app.logger.debug(f"✅ Response cached for {call_type} call (TTL: {cache_ttl_hours}h)")
        else:
            current_app.logger.warning(f"⚠️ Failed to cache response for {call_type} call")
        
        return {
            'response': response_content,
            'from_cache': False,
            'call_id': call_id,
            'metadata': response_metadata
        }
        
    except Exception as e:
        current_app.logger.error(f"Error in cached LLM call: {str(e)}")
        raise

def generate_prompts_for_logic(logic, sample_data, received_references=None, model_name=None):
    """
    Generate the system and user prompts that would be sent to the LLM for a given logic transformation.
    This is used for the preview-prompt endpoint to show what will be sent to the LLM.
    
    Args:
        logic (str): The logic transformation string
        sample_data (list): List of data rows (usually just the first row for preview)
        received_references (dict): Dictionary of references like notes and prompt template
        model_name (str): The name of the LLM model to use (defaults to DEFAULT_LLM_MODEL)
    
    Returns:
        tuple: (system_prompt, user_prompt) - The prompts that would be sent to the LLM
    """
    if received_references is None: 
        received_references = {}
    
    # Use default model if none provided
    if model_name is None:
        model_name = model_selection_service.get_model_for_operation()
    
    try:
        # --- Enhanced Data Resolution ---
        actual_target_column = received_references.get('target_column', '')
        actual_additional_data_worksheet = received_references.get('additional_data_worksheet', '')
        actual_additional_data_validation_column = received_references.get('additional_data_validation_column', '')

        # Infer target_column if not provided but relevant placeholders are used
        if not actual_target_column and any(p in logic for p in ["@prompt_additional_data_columnname", "@prompt_additional_data_column", "@prompt_additional_data_validation_column", "@prompt_additional_data"]):
            actual_target_column = "Produktfamilie" # Default context as per user guidance
            current_app.logger.info(f"Inferred target_column as '{actual_target_column}' for placeholder data resolution.")

        if actual_target_column and (not actual_additional_data_worksheet or not actual_additional_data_validation_column):
            try:
                from modules.google_sheets_service import google_sheets_service
                if google_sheets_service.is_authenticated(): # Ensure service is usable
                    mappings = google_sheets_service.read_mapping_definitions()
                    mapping_for_target = next((m for m in mappings if m.get('column') == actual_target_column), None)
                    if mapping_for_target and mapping_for_target.get('validation'):
                        validation_conf = mapping_for_target['validation']
                        if 'worksheet' in validation_conf and not actual_additional_data_worksheet:
                            actual_additional_data_worksheet = validation_conf['worksheet']
                            current_app.logger.info(f"Resolved additional_data_worksheet to '{actual_additional_data_worksheet}' for target '{actual_target_column}'.")
                        if 'column' in validation_conf and not actual_additional_data_validation_column:
                            actual_additional_data_validation_column = validation_conf['column']
                            current_app.logger.info(f"Resolved additional_data_validation_column to '{actual_additional_data_validation_column}' for target '{actual_target_column}'.")
            except Exception as e:
                current_app.logger.error(f"Error during enhanced data resolution for target '{actual_target_column}': {str(e)}")
        # --- End Enhanced Data Resolution ---

        rendered_logic_strings = []
        notes_ref_val = str(received_references.get('notes', ''))
        prompt_ref_val = str(received_references.get('prompt', ''))
        
        validation_data = received_references.get('validation_data', []) # This is for the general validation, not specific to @prompt_additional_data_validation_column
        
        for row_data in sample_data:
            current_rendered_logic = logic
            
            if "@row" in current_rendered_logic and isinstance(row_data, dict):
                # Properly handle Unicode characters in row data formatting
                formatted_row_items = []
                for k, v in row_data.items():
                    # Ensure proper Unicode handling for both key and value
                    safe_key = str(k).encode('utf-8', errors='replace').decode('utf-8') if k is not None else ""
                    safe_value = str(v).encode('utf-8', errors='replace').decode('utf-8') if v is not None else ""
                    formatted_row_items.append(f"{safe_key}: {safe_value}")
                formatted_row_string = "\n".join(formatted_row_items)  # Use actual newline, not escaped
                current_rendered_logic = current_rendered_logic.replace("@row", formatted_row_string)
            
            current_rendered_logic = current_rendered_logic.replace("@notes", notes_ref_val)

            # --- Robust Placeholder Replacement ---
            # Order: Longest/most specific @prompt_additional_... first, then shorter ones, then @prompt last.

            # @prompt_additional_data_validation_column (List of validation values from the *validation* column of the *additional data* worksheet)
            placeholder_tag = "@prompt_additional_data_validation_column"
            if placeholder_tag in current_rendered_logic:
                if actual_additional_data_worksheet and actual_additional_data_validation_column:
                    try:
                        from modules.google_sheets_service import google_sheets_service
                        validation_column_values = google_sheets_service.get_validation_data(actual_additional_data_worksheet, actual_additional_data_validation_column)
                        replacement_text = ", ".join(validation_column_values) if validation_column_values else "No validation data found"
                        current_rendered_logic = current_rendered_logic.replace(placeholder_tag, replacement_text)
                    except Exception as e:
                        # Use a more user-friendly fallback instead of an error message
                        fallback_text = "validation values from the reference data (currently unavailable)"
                        current_app.logger.debug(f"Could not load validation data for {placeholder_tag} (ws='{actual_additional_data_worksheet}', col='{actual_additional_data_validation_column}') - {str(e)}. Using fallback.")
                        current_rendered_logic = current_rendered_logic.replace(placeholder_tag, fallback_text)
                else:
                    # Provide a more graceful fallback instead of an error message
                    fallback_text = "validation values from the reference data"
                    current_app.logger.debug(f"Placeholder {placeholder_tag} could not be resolved (insufficient info), using fallback: '{fallback_text}'")
                    current_rendered_logic = current_rendered_logic.replace(placeholder_tag, fallback_text)

            # @prompt_additional_data_columnname (Name of the validation column itself)
            placeholder_tag = "@prompt_additional_data_columnname"
            if placeholder_tag in current_rendered_logic:
                if actual_additional_data_validation_column:
                    current_rendered_logic = current_rendered_logic.replace(placeholder_tag, actual_additional_data_validation_column)
                else:
                    # Provide a more graceful fallback instead of an error message
                    fallback_text = "the validation column"
                    current_app.logger.debug(f"Placeholder {placeholder_tag} could not be resolved, using fallback: '{fallback_text}'")
                    current_rendered_logic = current_rendered_logic.replace(placeholder_tag, fallback_text)
            
            # @prompt_additional_data_column (Old name for column name)
            placeholder_tag = "@prompt_additional_data_column"
            if placeholder_tag in current_rendered_logic:
                if actual_additional_data_validation_column: # Uses the same resolved column name
                    current_rendered_logic = current_rendered_logic.replace(placeholder_tag, actual_additional_data_validation_column)
                else:
                    # Provide a more graceful fallback instead of an error message
                    fallback_text = "the validation column"
                    current_app.logger.debug(f"Placeholder {placeholder_tag} could not be resolved, using fallback: '{fallback_text}'")
                    current_rendered_logic = current_rendered_logic.replace(placeholder_tag, fallback_text)

            # @prompt_additional_data (Markdown table from the *additional data* worksheet)
            placeholder_tag = "@prompt_additional_data"
            if placeholder_tag in current_rendered_logic:
                if actual_additional_data_worksheet:
                    try:
                        from modules.google_sheets_service import google_sheets_service
                        additional_data_markdown = google_sheets_service.get_worksheet_as_markdown_table(actual_additional_data_worksheet)
                        current_rendered_logic = current_rendered_logic.replace(placeholder_tag, additional_data_markdown)
                    except Exception as e:
                        # Use a more user-friendly fallback instead of an error message
                        fallback_text = "additional reference data (currently unavailable)"
                        current_app.logger.debug(f"Could not load worksheet data for {placeholder_tag} (ws='{actual_additional_data_worksheet}') - {str(e)}. Using fallback.")
                        current_rendered_logic = current_rendered_logic.replace(placeholder_tag, fallback_text)
                else:
                    # Provide a more graceful fallback instead of an error message
                    fallback_text = "additional reference data"
                    current_app.logger.debug(f"Placeholder {placeholder_tag} could not be resolved (no worksheet specified), using fallback: '{fallback_text}'")
                    current_rendered_logic = current_rendered_logic.replace(placeholder_tag, fallback_text)
            
            # @prompt (Column's prompt/description text) - Process LAST among @-placeholders
            current_rendered_logic = current_rendered_logic.replace("@prompt", prompt_ref_val)
            
            # @ColumnName (Specific column values from current row)
            if isinstance(row_data, dict):
                for col_name, col_val in row_data.items():
                    # Ensure col_name is a simple string to avoid issues if it contains regex special chars
                    # This regex escapes non-alphanumeric characters for safety if col_name were complex.
                    # However, @col_name placeholders are typically simple.
                    safe_col_placeholder = f"@{col_name}" # Assuming col_name is simple
                    # Ensure proper Unicode handling for column values
                    safe_col_value = str(col_val).encode('utf-8', errors='replace').decode('utf-8') if col_val is not None else ""
                    current_rendered_logic = current_rendered_logic.replace(safe_col_placeholder, safe_col_value)
            # --- End Robust Placeholder Replacement ---
            rendered_logic_strings.append(current_rendered_logic)
        
        # Check if we need to add worksheet data to the prompt
        worksheet_names = detect_worksheet_references(prompt_ref_val, actual_target_column)
        
        system_prompt = DATA_TRANSFORMATION_SYSTEM_PROMPT
        
        # Add validation instructions if validation data is provided
        if validation_data:
            system_prompt += f"""
If validation data is provided, you MUST ONLY return values from the validation list.
Select the most appropriate value from the validation list based on the context and logic.
If no appropriate value can be found in the validation list, select the closest match.
"""

        system_prompt += """
Ensure the output is ONLY the JSON object described, with no extraneous text or explanations.
"""

        # Conditionally include sample_data in the user_prompt
        should_include_sample_data_in_prompt = (any(f"@{col_name}" in logic for row_data in sample_data for col_name in (row_data.keys() if isinstance(row_data, dict) else [])) and not "@row" in logic)

        final_user_prompt_content = rendered_logic_strings[0] if rendered_logic_strings else logic

        # Add worksheet data to the user prompt if needed
        if worksheet_names:
            worksheet_data_text = "\n\n"
            for worksheet_name in worksheet_names:
                worksheet_df = get_worksheet_data(worksheet_name)
                if worksheet_df is not None:
                    # Create a markdown table from the DataFrame
                    markdown_table = df_to_markdown_table(worksheet_df)
                    worksheet_data_text += f'Worksheet "{worksheet_name}":\n\n{markdown_table}\n\n'
            
            final_user_prompt_content += worksheet_data_text

        # If the original logic did NOT contain @row but contained other @references, and we decided to include sample data:
        if should_include_sample_data_in_prompt and sample_data:
            final_user_prompt_content += f"""

Here's the sample data (input rows) for context to the @column references:
{json.dumps(sample_data, indent=2, ensure_ascii=False)}
"""
        elif not should_include_sample_data_in_prompt and not "@row" in logic and sample_data:
            final_user_prompt_content += f"\n\nApply the transformation logic {len(sample_data)} times (once for each item in the original request that requires a transformed value, if applicable)."
        elif not sample_data:
            final_user_prompt_content += "\n\nApply the transformation logic as provided."

        # Add validation data if provided
        if validation_data:
            final_user_prompt_content += f"""

You MUST ONLY select values from this validation list:
{json.dumps(validation_data, indent=2, ensure_ascii=False)}
Choose the most appropriate value from this list based on the context and logic.
"""
        
        return system_prompt, final_user_prompt_content
        
    except Exception as e:
        current_app.logger.error(f"Error generating prompts for logic: {str(e)}", exc_info=True)
        return f"Error generating system prompt: {str(e)}", f"Error generating user prompt: {str(e)}"

def detect_worksheet_references(text, target_column):
    """
    Detect references to Excel worksheets in text.
    
    Args:
        text (str): The text to analyze
        target_column (str): The target column name
        
    Returns:
        list: A list of worksheet names referenced in the text
    """
    if not text:
        return []
        
    worksheet_names = []
    
    # Use regex patterns to find worksheet references
    worksheet_regex_patterns = [
        r'im\s+Excelblatt\s+[\""]([^\"\"]+)[\""]',
        r'in\s+dem\s+Excelblatt\s+[\""]([^\"\"]+)[\""]',
        r'Registerkarte\s+([^\(\)\"\"\s]+)',  # Modified to exclude parentheses
        r'Worksheet\s+[\""]([^\"\"]+)[\""]',
        r'Excel\s+sheet\s+[\""]([^\"\"]+)[\""]'
    ]
    
    for pattern in worksheet_regex_patterns:
        matches = re.finditer(pattern, text, re.IGNORECASE)
        for match in matches:
            worksheet_name = match.group(1).strip()  # Strip whitespace
            if worksheet_name and worksheet_name not in worksheet_names:
                worksheet_names.append(worksheet_name)
    
    # Check for specific worksheet names mentioned in the text
    # Get actual worksheet names from Google Sheets
    try:
        from modules.google_sheets_service import google_sheets_service
        if google_sheets_service.is_authenticated():
            all_worksheet_names = google_sheets_service.get_sheet_names(google_sheets_service.mapping_sheet_id)
        else:
            all_worksheet_names = []
    except Exception as e:
        current_app.logger.warning(f"Could not get worksheet names for detection: {str(e)}")
        all_worksheet_names = []
    
    for name in all_worksheet_names:
        if name in text and name not in worksheet_names:
            worksheet_names.append(name)
    
    # Special handling for Produktfamilie column - only add if explicitly mentioned in text
    # Removed automatic addition to prevent unwanted worksheet data inclusion
        
    return worksheet_names

def df_to_markdown_table(df):
    """Convert a pandas DataFrame to a markdown table string"""
    if df.empty:
        return "Empty worksheet"
    
    # Create header row
    headers = df.columns.tolist()
    markdown = "| " + " | ".join(str(h) for h in headers) + " |\n"
    
    # Create separator row
    markdown += "|" + "|".join(["---" for _ in headers]) + "|\n"
    
    # Add data rows
    for _, row in df.iterrows():
        markdown += "| " + " | ".join(str(cell) if pd.notna(cell) else "" for cell in row) + " |\n"
    
    return markdown

def get_worksheet_data(worksheet_name):
    """Get a pandas DataFrame from the specified worksheet in Google Sheets. Excel fallback removed."""
    try:
        # Load from Google Sheets (required)
        from modules.google_sheets_service import google_sheets_service
        
        if not google_sheets_service.is_authenticated():
            raise Exception("Google Sheets authentication required to load worksheet data")
        
        worksheet_df = google_sheets_service.get_worksheet_data_as_dataframe(worksheet_name)
        if worksheet_df is not None:
            current_app.logger.info(f"Successfully loaded worksheet '{worksheet_name}' from Google Sheets")
            return worksheet_df
        else:
            current_app.logger.error(f"Worksheet '{worksheet_name}' not found in Google Sheets")
            return None
            
    except Exception as e:
        current_app.logger.error(f"Error reading worksheet {worksheet_name} from Google Sheets: {str(e)}", exc_info=True)
        raise Exception(f"Google Sheets authentication required to load worksheet '{worksheet_name}': {str(e)}")

def get_column_mappings(mapping_file_name_or_path):
    """Extract column mappings from Google Sheets. Excel fallback removed - Google Sheets authentication required."""
    try:
        # Load from Google Sheets (required)
        from modules.google_sheets_service import google_sheets_service
        
        mappings = google_sheets_service.read_mapping_definitions()
        if mappings:
            current_app.logger.info(f"Successfully loaded {len(mappings)} mapping definitions from Google Sheets")
            return mappings
        else:
            current_app.logger.error("No mapping definitions found in Google Sheets")
            return []
            
    except Exception as e:
        current_app.logger.error(f"Error loading mappings from Google Sheets: {str(e)}", exc_info=True)
        raise Exception(f"Google Sheets authentication required to load column mappings: {str(e)}")

def get_validation_data(mapping_file_path, worksheet_name, column_name):
    """Get validation data from specified worksheet and column in Google Sheets. Excel fallback removed."""
    try:
        # Load from Google Sheets (required)
        from modules.google_sheets_service import google_sheets_service
        
        validation_values = google_sheets_service.get_validation_data(worksheet_name, column_name)
        if validation_values:
            current_app.logger.info(f"Successfully loaded {len(validation_values)} validation values from Google Sheets")
            return validation_values
        else:
            current_app.logger.error(f"No validation data found for {worksheet_name}.{column_name} in Google Sheets")
            return []
            
    except Exception as e:
        current_app.logger.error(f"Error getting validation data from Google Sheets: {str(e)}", exc_info=True)
        raise Exception(f"Google Sheets authentication required to load validation data: {str(e)}")

def ai_column_mapping(source_columns, mapping_columns_with_prompts):
    """Use OpenRouter to map columns with high confidence."""
    try:
        if not openrouter_service.is_configured():
            current_app.logger.error("OpenRouter API key not found. Set OPENROUTER_API_KEY env var.")
            return []
        
        # mapping_columns_with_prompts is already the list of dicts with 'column' and 'prompt'
        # as prepared by get_column_mappings
        target_mapping_data = [
            {"column": col["column"], "description": col.get("prompt", "")}
            for col in mapping_columns_with_prompts
        ]

        system_prompt = COLUMN_MAPPING_SYSTEM_PROMPT
        user_prompt = f"""Here are the source columns:\n{json.dumps(source_columns, indent=2)}\n
Here are the target columns that need mapping:\n{json.dumps(target_mapping_data, indent=2)}\n
Analyze these and return only the high-confidence mappings... (rest as before)
Return a JSON object.""" # Explicitly ask for JSON
        
        # Get the model to use for this operation
        model_to_use = model_selection_service.get_model_for_operation()
        
        current_app.logger.info(f"🔧 Column Mapping Request to {model_to_use}")
        
        # Make cached LLM call
        llm_result = make_cached_llm_call(
            system_prompt=system_prompt,
            user_prompt=user_prompt,
            model_name=model_to_use,
            call_type="column_mapping",
            temperature=0.7,
            response_format={"type": "json_object"},
            cache_ttl_hours=168  # Cache column mappings for 1 week
        )
        
        response_content = llm_result['response']
        
        if llm_result['from_cache']:
            current_app.logger.info(f"📊 Column Mapping Response (cached): {safe_str_for_logging(response_content, 200)}")
        else:
            current_app.logger.info(f"📊 Column Mapping Response [ID: {llm_result.get('call_id')}]: {safe_str_for_logging(response_content, 200)}")
        
        mapping_results = json.loads(response_content).get("mappings", [])
        current_app.logger.info(f"AI Mapping Results: {mapping_results}") # Log extracted mappings
        current_app.logger.info(f"AI successfully mapped {len(mapping_results)} columns")
        return mapping_results
    except Exception as e:
        current_app.logger.error(f"Error in AI column mapping: {str(e)}", exc_info=True)
        return []

def apply_classification_with_retry_and_self_repair(prompt, validation_data, model_name=None, rate_limit_callback=None):
    """
    Advanced classification function with retry and self-repair mechanism.
    
    Args:
        prompt (str): The prompt to send to the LLM
        validation_data (list): List of valid classification values
        model_name (str): The name of the LLM model to use (defaults to model selection service)
    
    Returns:
        str: The classified value or error message
        """
    # Use default model if none provided
    if model_name is None:
        model_name = model_selection_service.get_model_for_operation()
        
    current_app.logger.info(f"🎯 Starting classification with {len(validation_data)} validation options")
    
    # Convert validation data to uppercase for case-insensitive comparison
    valid_codes = {code.strip().upper() for code in validation_data}
    error_code = "ERROR: UNSURE WHICH PRODUCT FAMILY APPLIES"
    
    # Add error option to valid codes
    valid_codes.add(error_code)
    
    # First attempt with normal temperature
    try:
        system_prompt = CLASSIFICATION_SYSTEM_PROMPT
        
        current_app.logger.info(f"🎲 Attempt 1 (temp=0.7)")
        
        if not openrouter_service.is_configured():
            current_app.logger.error("OpenRouter API key not found for classification")
            return "Error: OpenRouter API Key missing"
        
        # Make cached LLM call - first attempt
        llm_result = make_cached_llm_call(
            system_prompt=system_prompt,
            user_prompt=prompt,
            model_name=model_name,
            call_type="classification_attempt1",
            temperature=0.7,
            cache_ttl_hours=24,  # Cache classifications for 1 day
            rate_limit_callback=rate_limit_callback
        )
        
        result = llm_result['response'].strip()
        
        if llm_result['from_cache']:
            current_app.logger.info(f"📋 Result (cached): '{result}'")
        else:
            current_app.logger.info(f"📋 Result: '{result}' [ID: {llm_result.get('call_id')}]")
        
        # Validate response
        if result.upper() in valid_codes:
            current_app.logger.info(f"✅ Success: {result}")
            return result
        
        # First attempt failed, try with lower temperature
        current_app.logger.info("🎲 Attempt 2 (temp=0.4)")
        
        llm_result = make_cached_llm_call(
            system_prompt=system_prompt,
            user_prompt=prompt,
            model_name=model_name,
            call_type="classification_attempt2",
            temperature=0.4,
            cache_ttl_hours=24,
            rate_limit_callback=rate_limit_callback
        )
        
        result = llm_result['response'].strip()
        current_app.logger.info(f"📋 Result: '{result}'")
        
        # Validate second attempt
        if result.upper() in valid_codes:
            current_app.logger.info(f"✅ Success: {result}")
            return result
        
        # Both attempts failed, try self-repair
        current_app.logger.info(f"🔧 Self-repair (temp=0.2)")
        
        # Create self-repair prompt
        self_repair_prompt = f"""The following response is invalid: "{result}"

Please correct this by choosing ONLY from the valid product family codes listed below.

Return ONLY the correct code (e.g., SANDKAESTEN), or "ERROR: Unsure which product family applies".

Valid codes:
{json.dumps(list(validation_data), indent=2, ensure_ascii=False)}

Product Information:
{prompt}
"""
        
        # Make cached LLM call for self-repair
        llm_result = make_cached_llm_call(
            system_prompt=system_prompt,
            user_prompt=self_repair_prompt,
            model_name=model_name,
            call_type="classification_self_repair",
            temperature=0.2,
            cache_ttl_hours=24,
            rate_limit_callback=rate_limit_callback
        )
        
        result = llm_result['response'].strip()
        current_app.logger.info(f"📋 Result: '{result}'")
        
        # Validate self-repair result
        if result.upper() in valid_codes:
            current_app.logger.info(f"✅ Success: {result}")
            return result
        
        # All attempts failed
        current_app.logger.error(f"❌ All attempts failed: '{result}'")
        return error_code
        
    except Exception as e:
        current_app.logger.error(f"Error in classification with retry: {str(e)}", exc_info=True)
        return f"Error: {str(e)}"

def apply_logic_transformation(logic, sample_data, received_references=None, model_name=None, rate_limit_callback=None):
    """Use LLM to transform data based on user-provided logic and generate rendered logic strings."""
    # Use default model if none provided
    if model_name is None:
        model_name = model_selection_service.get_model_for_operation()
    
    current_app.logger.info(f"🚀 Starting transformation: {len(sample_data)} rows, model: {model_name}")
    if received_references is None: received_references = {}
    raw_llm_responses = [None] * len(sample_data)
    try:
        # --- Enhanced Data Resolution ---
        actual_target_column = received_references.get('target_column', '')
        actual_additional_data_worksheet = received_references.get('additional_data_worksheet', '')
        actual_additional_data_validation_column = received_references.get('additional_data_validation_column', '')

        if not actual_target_column and any(p in logic for p in ["@prompt_additional_data_columnname", "@prompt_additional_data_column", "@prompt_additional_data_validation_column", "@prompt_additional_data"]):
            actual_target_column = "Produktfamilie"
            current_app.logger.info(f"Inferred target_column as '{actual_target_column}' for placeholder data resolution (apply_logic).")

        if actual_target_column and (not actual_additional_data_worksheet or not actual_additional_data_validation_column):
            try:
                from modules.google_sheets_service import google_sheets_service
                if google_sheets_service.is_authenticated():
                    mappings = google_sheets_service.read_mapping_definitions()
                    mapping_for_target = next((m for m in mappings if m.get('column') == actual_target_column), None)
                    if mapping_for_target and mapping_for_target.get('validation'):
                        validation_conf = mapping_for_target['validation']
                        if 'worksheet' in validation_conf and not actual_additional_data_worksheet:
                            actual_additional_data_worksheet = validation_conf['worksheet']
                            current_app.logger.info(f"Resolved additional_data_worksheet to '{actual_additional_data_worksheet}' for target '{actual_target_column}' (apply_logic).")
                        if 'column' in validation_conf and not actual_additional_data_validation_column:
                            actual_additional_data_validation_column = validation_conf['column']
                            current_app.logger.info(f"Resolved additional_data_validation_column to '{actual_additional_data_validation_column}' for target '{actual_target_column}' (apply_logic).")
            except Exception as e:
                current_app.logger.error(f"Error during enhanced data resolution for target '{actual_target_column}' (apply_logic): {str(e)}")
        # --- End Enhanced Data Resolution ---

        if not openrouter_service.is_configured():
            current_app.logger.error("OpenRouter API key not found. Set OPENROUTER_API_KEY env var.")
            return {"transformed_values": ["Error: OpenRouter API Key missing"] * len(sample_data) if sample_data else [], 
                    "rendered_logic_strings": [logic] * len(sample_data) if sample_data else [],
                    "raw_llm_responses": raw_llm_responses}
        
        rendered_logic_strings = []
        notes_ref_val = str(received_references.get('notes', ''))
        prompt_ref_val = str(received_references.get('prompt', ''))
        
        validation_data = received_references.get('validation_data', [])
        
        for row_data in sample_data:
            current_rendered_logic = logic
            
            if "@row" in current_rendered_logic and isinstance(row_data, dict):
                # Properly handle Unicode characters in row data formatting
                formatted_row_items = []
                for k, v in row_data.items():
                    # Ensure proper Unicode handling for both key and value
                    safe_key = str(k).encode('utf-8', errors='replace').decode('utf-8') if k is not None else ""
                    safe_value = str(v).encode('utf-8', errors='replace').decode('utf-8') if v is not None else ""
                    formatted_row_items.append(f"{safe_key}: {safe_value}")
                formatted_row_string = "\n".join(formatted_row_items)  # Use actual newline, not escaped
                current_rendered_logic = current_rendered_logic.replace("@row", formatted_row_string)
            
            current_rendered_logic = current_rendered_logic.replace("@notes", notes_ref_val)

            # --- Robust Placeholder Replacement ---
            # Order: Longest/most specific @prompt_additional_... first, then shorter ones, then @prompt last.

            # @prompt_additional_data_validation_column
            placeholder_tag = "@prompt_additional_data_validation_column"
            if placeholder_tag in current_rendered_logic:
                if actual_additional_data_worksheet and actual_additional_data_validation_column:
                    try:
                        from modules.google_sheets_service import google_sheets_service
                        validation_column_values = google_sheets_service.get_validation_data(actual_additional_data_worksheet, actual_additional_data_validation_column)
                        replacement_text = ", ".join(validation_column_values) if validation_column_values else "No validation data found"
                        current_rendered_logic = current_rendered_logic.replace(placeholder_tag, replacement_text)
                    except Exception as e:
                        # Use a more user-friendly fallback instead of an error message
                        fallback_text = "validation values from the reference data (currently unavailable)"
                        current_app.logger.debug(f"Could not load validation data for {placeholder_tag} (ws='{actual_additional_data_worksheet}', col='{actual_additional_data_validation_column}') - {str(e)}. Using fallback.")
                        current_rendered_logic = current_rendered_logic.replace(placeholder_tag, fallback_text)
                else:
                    # Provide a more graceful fallback instead of an error message
                    fallback_text = "validation values from the reference data"
                    current_app.logger.debug(f"Placeholder {placeholder_tag} could not be resolved (insufficient info), using fallback: '{fallback_text}'")
                    current_rendered_logic = current_rendered_logic.replace(placeholder_tag, fallback_text)

            # @prompt_additional_data_columnname
            placeholder_tag = "@prompt_additional_data_columnname"
            if placeholder_tag in current_rendered_logic:
                if actual_additional_data_validation_column:
                    current_rendered_logic = current_rendered_logic.replace(placeholder_tag, actual_additional_data_validation_column)
                else:
                    # Provide a more graceful fallback instead of an error message
                    fallback_text = "the validation column"
                    current_app.logger.debug(f"Placeholder {placeholder_tag} could not be resolved, using fallback: '{fallback_text}'")
                    current_rendered_logic = current_rendered_logic.replace(placeholder_tag, fallback_text)
            
            # @prompt_additional_data_column
            placeholder_tag = "@prompt_additional_data_column"
            if placeholder_tag in current_rendered_logic:
                if actual_additional_data_validation_column:
                    current_rendered_logic = current_rendered_logic.replace(placeholder_tag, actual_additional_data_validation_column)
                else:
                    # Provide a more graceful fallback instead of an error message
                    fallback_text = "the validation column"
                    current_app.logger.debug(f"Placeholder {placeholder_tag} could not be resolved, using fallback: '{fallback_text}'")
                    current_rendered_logic = current_rendered_logic.replace(placeholder_tag, fallback_text)

            # @prompt_additional_data
            placeholder_tag = "@prompt_additional_data"
            if placeholder_tag in current_rendered_logic:
                if actual_additional_data_worksheet:
                    try:
                        from modules.google_sheets_service import google_sheets_service
                        additional_data_markdown = google_sheets_service.get_worksheet_as_markdown_table(actual_additional_data_worksheet)
                        current_rendered_logic = current_rendered_logic.replace(placeholder_tag, additional_data_markdown)
                    except Exception as e:
                        # Use a more user-friendly fallback instead of an error message
                        fallback_text = "additional reference data (currently unavailable)"
                        current_app.logger.debug(f"Could not load worksheet data for {placeholder_tag} (ws='{actual_additional_data_worksheet}') - {str(e)}. Using fallback.")
                        current_rendered_logic = current_rendered_logic.replace(placeholder_tag, fallback_text)
                else:
                    # Provide a more graceful fallback instead of an error message
                    fallback_text = "additional reference data"
                    current_app.logger.debug(f"Placeholder {placeholder_tag} could not be resolved (no worksheet specified), using fallback: '{fallback_text}'")
                    current_rendered_logic = current_rendered_logic.replace(placeholder_tag, fallback_text)
            
            # @prompt - Process LAST among @-placeholders
            current_rendered_logic = current_rendered_logic.replace("@prompt", prompt_ref_val)
            
            # @ColumnName
            if isinstance(row_data, dict):
                for col_name, col_val in row_data.items():
                    # Ensure col_name is a simple string to avoid issues if it contains regex special chars
                    # This regex escapes non-alphanumeric characters for safety if col_name were complex.
                    # However, @col_name placeholders are typically simple.
                    safe_col_placeholder = f"@{col_name}" # Assuming col_name is simple
                    # Ensure proper Unicode handling for column values
                    safe_col_value = str(col_val).encode('utf-8', errors='replace').decode('utf-8') if col_val is not None else ""
                    current_rendered_logic = current_rendered_logic.replace(safe_col_placeholder, safe_col_value)
            # --- End Robust Placeholder Replacement ---
            rendered_logic_strings.append(current_rendered_logic)
        
        # Check if we need to add worksheet data to the prompt
        worksheet_names = detect_worksheet_references(prompt_ref_val, actual_target_column)
        
        # Check if this is a classification task that needs robust retry and validation
        # For now, we'll detect this based on having validation_data and the target column being 'Produktfamilie'
        is_classification_task = bool(validation_data) and actual_target_column == 'Produktfamilie'
        
        if is_classification_task and sample_data:
            current_app.logger.info(f"Using classification with retry and self-repair for '{actual_target_column}'")
            transformed_values = []
            
            for idx, row_logic in enumerate(rendered_logic_strings):
                # Add worksheet data to row_logic if needed
                row_prompt = row_logic
                if worksheet_names:
                    worksheet_data_text = "\n\n"
                    for worksheet_name in worksheet_names:
                        worksheet_df = get_worksheet_data(worksheet_name)
                        if worksheet_df is not None:
                            markdown_table = df_to_markdown_table(worksheet_df)
                            worksheet_data_text += f'Worksheet "{worksheet_name}":\n\n{markdown_table}\n\n'
                    row_prompt += worksheet_data_text
                
                # Use classification with retry and self-repair
                classification_result = apply_classification_with_retry_and_self_repair(
                    row_prompt,
                    validation_data,
                    model_name,
                    rate_limit_callback=rate_limit_callback
                )
                transformed_values.append(classification_result)
            
            raw_llm_responses = ["Used classification with retry and self-repair"] * len(sample_data)
            
            result = {
                "transformed_values": transformed_values,
                "rendered_logic_strings": rendered_logic_strings,
                "raw_llm_responses": raw_llm_responses
            }
            current_app.logger.info(f"🎯 Classification complete: {len(transformed_values)} items classified")
            return result
        
        # Standard non-classification logic continues below
        system_prompt = DEFAULT_SYSTEM_PROMPT

        
        # Add validation instructions if validation data is provided
        if validation_data:
            system_prompt += f"""
If validation data is provided, you MUST ONLY return values from the validation list.
Select the most appropriate value from the validation list based on the context and logic.
If no appropriate value can be found in the validation list, select the closest match.
"""

        system_prompt += """
Ensure the output is ONLY the JSON object described, with no extraneous text or explanations.
"""

        # Conditionally include sample_data in the user_prompt
        should_include_sample_data_in_prompt = (any(f"@{col_name}" in logic for row_data in sample_data for col_name in (row_data.keys() if isinstance(row_data, dict) else [])) and not "@row" in logic)

        # Start with the first rendered logic string as base for the user prompt
        base_user_prompt = rendered_logic_strings[0] if rendered_logic_strings else logic
        
        # Add worksheet data to the user prompt if needed
        if worksheet_names:
            worksheet_data_text = "\n\n"
            for worksheet_name in worksheet_names:
                worksheet_df = get_worksheet_data(worksheet_name)
                if worksheet_df is not None:
                    # Create a markdown table from the DataFrame
                    markdown_table = df_to_markdown_table(worksheet_df)
                    worksheet_data_text += f'Worksheet "{worksheet_name}":\n\n{markdown_table}\n\n'
            
            base_user_prompt += worksheet_data_text

        # Build final user prompt with appropriate context and instructions
        final_user_prompt = base_user_prompt
        
        # If the original logic did NOT contain @row but contained other @references, and we decided to include sample data:
        if should_include_sample_data_in_prompt and sample_data:
            final_user_prompt += f"""

Here's the sample data (input rows) for context to the @column references:
{json.dumps(sample_data, indent=2, ensure_ascii=False)}
"""
        elif not should_include_sample_data_in_prompt and not "@row" in logic and sample_data:
            final_user_prompt += f"\n\nApply the transformation logic {len(sample_data)} times (once for each item in the original request that requires a transformed value, if applicable)."
        elif not sample_data:
            final_user_prompt += "\n\nApply the transformation logic as provided."

        # Add validation data if provided
        if validation_data:
            final_user_prompt += f"""

You MUST ONLY select values from this validation list:
{json.dumps(validation_data, indent=2, ensure_ascii=False)}
Choose the most appropriate value from this list based on the context and logic.
"""

        # Concise logging for LLM requests
        current_app.logger.info(f"🤖 LLM Request to {model_name}")
        current_app.logger.info(f"📝 System Prompt: {safe_str_for_logging(system_prompt, 100)}")
        current_app.logger.info(f"📋 User Prompt: {safe_str_for_logging(final_user_prompt, 150)}")
        
        # Make cached LLM call
        llm_result = make_cached_llm_call(
            system_prompt=system_prompt,
            user_prompt=final_user_prompt,
            model_name=model_name,
            call_type="transformation",
            temperature=0.7,
            response_format={"type": "json_object"},
            cache_ttl_hours=12,  # Cache transformations for 12 hours
            rate_limit_callback=rate_limit_callback
        )
        
        response_content = llm_result['response']
        current_app.logger.debug(f"Raw response content: {response_content}")
        
        # Clear and prominent LLM response logging
        if llm_result['from_cache']:
            current_app.logger.info(f"✅ LLM Response received (cached)")
        else:
            current_app.logger.info(f"✅ LLM Response received [ID: {llm_result.get('call_id')}]")
        current_app.logger.info(f"🔄 Raw LLM Response: {safe_str_for_logging(response_content, 500)}")
        
        try:
            result_json = json.loads(response_content)
            transformed_values = result_json.get("transformed_values") or []
            
            # Fix: If LLM returns a string/int/other type instead of array, convert to array
            if isinstance(transformed_values, str):
                current_app.logger.info(f"LLM returned string instead of array, converting: {transformed_values}")
                transformed_values = [transformed_values]
            elif isinstance(transformed_values, (int, float)):
                current_app.logger.info(f"LLM returned number instead of array, converting: {transformed_values}")
                transformed_values = [str(transformed_values)]
            elif not isinstance(transformed_values, list):
                current_app.logger.info(f"LLM returned {type(transformed_values)} instead of array, converting: {transformed_values}")
                transformed_values = [str(transformed_values)]
            
            current_app.logger.info(f"📤 LLM Transformed Values: {safe_str_for_logging(transformed_values, 400)}")
            
            # If we have a mismatch between sample_data and transformed_values,
            # pad or truncate transformed_values to match
            if len(transformed_values) != len(sample_data) and sample_data:
                current_app.logger.warning(f"Mismatch between transformed_values ({len(transformed_values)}) and sample_data ({len(sample_data)})")
                if len(transformed_values) < len(sample_data):
                    # Pad with last value or empty string
                    pad_value = transformed_values[-1] if transformed_values else ""
                    transformed_values.extend([pad_value] * (len(sample_data) - len(transformed_values)))
                elif len(transformed_values) > len(sample_data):
                    # Truncate
                    transformed_values = transformed_values[:len(sample_data)]
            
            raw_llm_responses = [response_content] * len(sample_data)
            
            result = {
                "transformed_values": transformed_values,
                "rendered_logic_strings": rendered_logic_strings,
                "raw_llm_responses": raw_llm_responses,
                "call_id": llm_result.get('call_id'),
                "from_cache": llm_result['from_cache']
            }
            current_app.logger.info(f"✨ Final Result: {len(transformed_values)} values transformed successfully")
            return result
        except json.JSONDecodeError:
            current_app.logger.error(f"Error parsing LLM response as JSON: {response_content}")
            return {
                "transformed_values": ["Error: Invalid LLM response format"] * len(sample_data) if sample_data else [],
                "rendered_logic_strings": rendered_logic_strings,
                "raw_llm_responses": [response_content] * len(sample_data) if sample_data else [],
                "call_id": llm_result.get('call_id'),
                "from_cache": llm_result['from_cache']
            }
    except Exception as e:
        current_app.logger.error(f"Error in apply_logic_transformation: {str(e)}", exc_info=True)
        return {
            "transformed_values": [f"Error: {str(e)}"] * len(sample_data) if sample_data else [],
            "rendered_logic_strings": [logic] * len(sample_data) if sample_data else [],
            "raw_llm_responses": raw_llm_responses,
            "call_id": None,
            "from_cache": False
        }

def ai_column_mapping_optimized(source_columns, target_mapping_definitions):
    """
    PERFORMANCE OPTIMIZED: Enhanced AI column mapping with caching and improved algorithms.

    Args:
        source_columns: List of source column names
        target_mapping_definitions: List of target column definitions

    Returns:
        List of mapped columns with confidence scores
    """
    import time
    from models.redis_models import RedisLLMCacheManager

    start_time = time.time()
    cache_manager = RedisLLMCacheManager()

    # Create cache key for this mapping request
    import hashlib
    import json
    cache_key_data = {
        'source_columns': sorted(source_columns),
        'target_definitions': sorted([str(td) for td in target_mapping_definitions])
    }
    cache_key = hashlib.md5(json.dumps(cache_key_data, sort_keys=True).encode()).hexdigest()

    # Try to get from cache first
    cached_result = cache_manager.redis.get(f"column_mapping_cache:{cache_key}")
    if cached_result:
        try:
            cached_mappings = json.loads(cached_result)
            current_app.logger.info(f"🚀 Cache hit for column mapping: {len(cached_mappings)} mappings")
            return cached_mappings
        except json.JSONDecodeError:
            pass

    current_app.logger.info(f"🔍 Computing AI column mapping for {len(source_columns)} → {len(target_mapping_definitions)} columns")

    # OPTIMIZATION 1: Pre-filter obvious matches using string similarity
    from difflib import SequenceMatcher

    def similarity_score(a, b):
        """Calculate similarity between two strings"""
        return SequenceMatcher(None, a.lower(), b.lower()).ratio()

    # OPTIMIZATION 2: Create quick similarity matrix
    quick_matches = []
    for target_def in target_mapping_definitions:
        target_column = target_def.get('column', '')
        target_desc = target_def.get('description', '')

        best_match = None
        best_score = 0

        for source_col in source_columns:
            # Check direct name similarity
            name_score = similarity_score(source_col, target_column)

            # Check description similarity if available
            desc_score = 0
            if target_desc:
                desc_score = similarity_score(source_col, target_desc)

            # Combined score with name weighted higher
            combined_score = (name_score * 0.7) + (desc_score * 0.3)

            if combined_score > best_score and combined_score > 0.6:  # Threshold for quick match
                best_score = combined_score
                best_match = source_col

        if best_match:
            quick_matches.append({
                'target_column': target_column,
                'source_column': best_match,
                'confidence': round(best_score * 100, 1),
                'method': 'similarity'
            })

    # OPTIMIZATION 3: Use AI only for remaining unmapped columns
    mapped_sources = {match['source_column'] for match in quick_matches}
    mapped_targets = {match['target_column'] for match in quick_matches}

    remaining_sources = [col for col in source_columns if col not in mapped_sources]
    remaining_targets = [td for td in target_mapping_definitions if td.get('column') not in mapped_targets]

    ai_mappings = []
    if remaining_sources and remaining_targets and len(remaining_targets) <= 20:  # Only use AI for reasonable sizes
        current_app.logger.info(f"🤖 Using AI for {len(remaining_sources)} → {len(remaining_targets)} remaining columns")
        try:
            ai_mappings = ai_column_mapping(remaining_sources, remaining_targets)
            # Add method tag to AI mappings
            for mapping in ai_mappings:
                mapping['method'] = 'ai'
        except Exception as e:
            current_app.logger.warning(f"⚠️ AI mapping failed, using fallback: {e}")
            # Fallback to simple matching for remaining columns
            for target_def in remaining_targets[:10]:  # Limit to prevent performance issues
                target_column = target_def.get('column', '')
                # Find best remaining source match
                best_match = None
                best_score = 0
                for source_col in remaining_sources:
                    score = similarity_score(source_col, target_column)
                    if score > best_score and score > 0.3:
                        best_score = score
                        best_match = source_col

                if best_match:
                    ai_mappings.append({
                        'target_column': target_column,
                        'source_column': best_match,
                        'confidence': round(best_score * 100, 1),
                        'method': 'fallback'
                    })

    # Combine all mappings
    all_mappings = quick_matches + ai_mappings

    # OPTIMIZATION 4: Sort by confidence and remove duplicates
    seen_targets = set()
    seen_sources = set()
    final_mappings = []

    # Sort by confidence (highest first)
    all_mappings.sort(key=lambda x: x.get('confidence', 0), reverse=True)

    for mapping in all_mappings:
        target = mapping['target_column']
        source = mapping['source_column']

        # Avoid duplicate mappings
        if target not in seen_targets and source not in seen_sources:
            final_mappings.append(mapping)
            seen_targets.add(target)
            seen_sources.add(source)

    # Cache the result for future use
    try:
        cache_manager.redis.setex(
            f"column_mapping_cache:{cache_key}",
            3600,  # 1 hour cache
            json.dumps(final_mappings)
        )
    except Exception as e:
        current_app.logger.warning(f"⚠️ Failed to cache mapping result: {e}")

    processing_time = time.time() - start_time
    current_app.logger.info(f"✅ Optimized column mapping completed in {processing_time:.2f}s: {len(final_mappings)} mappings")

    return final_mappings