"""
Performance-optimized LLM processing module for Akeneo import tool.
Implements async processing, intelligent batching, and advanced caching.
"""

import asyncio
import time
from typing import List, Dict, Any, Optional, Callable
from concurrent.futures import ThreadPoolExecutor, as_completed
import json
from flask import current_app

from modules.utils.llm_helpers import make_cached_llm_call
from modules.utils.model_selection_service import model_selection_service
from models.redis_models import RedisJobDataManager, RedisLLMCacheManager


class PerformanceOptimizedLLMProcessor:
    """High-performance LLM processor with async capabilities and intelligent batching"""
    
    def __init__(self, job_id: str):
        self.job_id = job_id
        self.data_manager = RedisJobDataManager(job_id)
        self.cache_manager = RedisLLMCacheManager()
        
        # Performance settings
        self.max_concurrent_requests = 5
        self.optimal_batch_size = 50
        self.max_batch_size = 100
        self.cache_ttl_hours = 24
        
        # Rate limiting
        self.request_delay = 0.1  # 100ms between requests
        self.last_request_time = 0
        
    def calculate_optimal_batch_size(self, total_rows: int, logic_complexity: str = "medium") -> int:
        """Calculate optimal batch size based on data size and logic complexity"""
        
        # Base batch size on logic complexity
        complexity_multipliers = {
            "simple": 1.5,    # Simple transformations can handle larger batches
            "medium": 1.0,    # Default
            "complex": 0.7    # Complex logic needs smaller batches
        }
        
        multiplier = complexity_multipliers.get(logic_complexity, 1.0)
        calculated_size = int(self.optimal_batch_size * multiplier)
        
        # Adjust based on total data size
        if total_rows < 100:
            calculated_size = min(calculated_size, 20)
        elif total_rows < 1000:
            calculated_size = min(calculated_size, 50)
        else:
            calculated_size = min(calculated_size, self.max_batch_size)
        
        return max(10, calculated_size)  # Minimum batch size of 10
    
    def preprocess_logic_for_batching(self, logic: str, sample_data: List[Dict]) -> Dict[str, Any]:
        """Analyze logic to determine optimal processing strategy"""
        
        # Detect if logic uses row-specific references
        uses_row_references = "@row" in logic
        uses_column_references = any(f"@{col}" in logic for col in sample_data[0].keys() if sample_data)
        
        # Estimate complexity
        complexity_indicators = [
            len(logic) > 500,  # Long logic
            logic.count("if") > 2,  # Multiple conditions
            "complex" in logic.lower() or "calculate" in logic.lower(),
            uses_row_references and uses_column_references
        ]
        
        complexity_score = sum(complexity_indicators)
        if complexity_score >= 3:
            complexity = "complex"
        elif complexity_score >= 1:
            complexity = "medium"
        else:
            complexity = "simple"
        
        return {
            "complexity": complexity,
            "uses_row_references": uses_row_references,
            "uses_column_references": uses_column_references,
            "can_batch_process": not uses_row_references,  # Row references require individual processing
            "recommended_batch_size": self.calculate_optimal_batch_size(len(sample_data), complexity)
        }
    
    async def async_process_batch(self, logic: str, batch_data: List[Dict], 
                                 references: Dict, model_name: str,
                                 batch_id: int, progress_callback: Optional[Callable] = None) -> Dict[str, Any]:
        """Process a single batch asynchronously"""
        
        try:
            start_time = time.time()
            
            # Apply rate limiting
            current_time = time.time()
            time_since_last = current_time - self.last_request_time
            if time_since_last < self.request_delay:
                await asyncio.sleep(self.request_delay - time_since_last)
            
            self.last_request_time = time.time()
            
            # Process the batch using existing LLM helpers
            from modules.utils.llm_helpers import apply_logic_transformation
            
            # Run in thread pool to avoid blocking
            loop = asyncio.get_event_loop()
            with ThreadPoolExecutor(max_workers=1) as executor:
                result = await loop.run_in_executor(
                    executor,
                    apply_logic_transformation,
                    logic,
                    batch_data,
                    references,
                    model_name
                )
            
            processing_time = time.time() - start_time
            
            # Update progress if callback provided
            if progress_callback:
                await progress_callback(batch_id, len(batch_data), processing_time)
            
            current_app.logger.info(f"Batch {batch_id} processed in {processing_time:.2f}s ({len(batch_data)} rows)")
            
            return {
                "success": True,
                "batch_id": batch_id,
                "results": result.get('transformed_values', []),
                "processing_time": processing_time,
                "rows_processed": len(batch_data)
            }
            
        except Exception as e:
            current_app.logger.error(f"Error processing batch {batch_id}: {str(e)}")
            return {
                "success": False,
                "batch_id": batch_id,
                "error": str(e),
                "rows_processed": 0
            }
    
    async def process_large_dataset_async(self, logic: str, target_column: str, 
                                        references: Dict, model_name: str = None,
                                        progress_callback: Optional[Callable] = None) -> Dict[str, Any]:
        """Process large dataset with async batch processing for maximum performance"""
        
        if not model_name:
            model_name = model_selection_service.get_model_for_operation()
        
        # Get total row count
        metadata = self.data_manager.get_job_metadata()
        total_rows = int(metadata.get('row_count', 0))
        
        if total_rows == 0:
            return {"success": True, "message": "No data to process", "total_processed": 0}
        
        # Get sample data for analysis
        sample_page = self.data_manager.get_data_page(1, 10)
        sample_data = sample_page.get('rows', [])
        
        if not sample_data:
            return {"success": False, "error": "No sample data available"}
        
        # Analyze logic and determine optimal processing strategy
        analysis = self.preprocess_logic_for_batching(logic, sample_data)
        batch_size = analysis["recommended_batch_size"]
        
        current_app.logger.info(f"Processing {total_rows} rows with batch size {batch_size} (complexity: {analysis['complexity']})")
        
        # Calculate total pages needed
        total_pages = (total_rows + batch_size - 1) // batch_size
        
        # Process batches concurrently
        semaphore = asyncio.Semaphore(self.max_concurrent_requests)
        
        async def process_page_with_semaphore(page_num):
            async with semaphore:
                # Get page data
                page_data = self.data_manager.get_data_page(page_num, batch_size)
                rows = page_data.get('rows', [])
                
                if not rows:
                    return {"success": True, "batch_id": page_num, "rows_processed": 0}
                
                return await self.async_process_batch(
                    logic, rows, references, model_name, page_num, progress_callback
                )
        
        # Create tasks for all pages
        tasks = [process_page_with_semaphore(page) for page in range(1, total_pages + 1)]
        
        # Process all batches concurrently
        start_time = time.time()
        results = await asyncio.gather(*tasks, return_exceptions=True)
        total_time = time.time() - start_time
        
        # Aggregate results and update data
        total_processed = 0
        total_errors = 0
        bulk_updates = []
        
        for result in results:
            if isinstance(result, Exception):
                current_app.logger.error(f"Batch processing exception: {result}")
                total_errors += 1
                continue
            
            if not result.get("success"):
                total_errors += 1
                continue
            
            batch_id = result["batch_id"]
            batch_results = result.get("results", [])
            rows_processed = result.get("rows_processed", 0)
            
            # Prepare bulk updates
            for i, transformed_value in enumerate(batch_results):
                row_index = (batch_id - 1) * batch_size + i
                bulk_updates.append({
                    'row_index': row_index,
                    'column_name': target_column,
                    'value': transformed_value
                })
            
            total_processed += rows_processed
        
        # Apply all updates in bulk
        if bulk_updates:
            update_success = self.data_manager.bulk_update_cells(bulk_updates)
            if not update_success:
                current_app.logger.warning("Some bulk updates may have failed")
        
        # Calculate performance metrics
        rows_per_second = total_processed / total_time if total_time > 0 else 0
        
        return {
            "success": total_errors == 0,
            "total_processed": total_processed,
            "total_errors": total_errors,
            "processing_time": total_time,
            "rows_per_second": round(rows_per_second, 2),
            "batches_processed": len([r for r in results if not isinstance(r, Exception)]),
            "performance_metrics": {
                "batch_size": batch_size,
                "concurrent_requests": self.max_concurrent_requests,
                "complexity": analysis["complexity"],
                "cache_hit_rate": "N/A"  # TODO: Calculate from cache manager
            }
        }


def run_async_processing(processor: PerformanceOptimizedLLMProcessor, *args, **kwargs):
    """Helper function to run async processing in sync context"""
    try:
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        return loop.run_until_complete(processor.process_large_dataset_async(*args, **kwargs))
    finally:
        loop.close()
