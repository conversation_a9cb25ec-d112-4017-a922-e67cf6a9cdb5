import os
import json
import io
import pandas as pd # For /api/import/export
import time
import hashlib
from functools import wraps
from flask import (
    Blueprint, jsonify, request, render_template, current_app, make_response, Response
)
from werkzeug.utils import secure_filename
from modules.auth import requires_auth
from modules.utils.file_processors import process_csv_file, process_excel_file
from modules.utils.llm_helpers import get_column_mappings, ai_column_mapping, apply_logic_transformation, generate_prompts_for_logic
from modules.utils.model_selection_service import model_selection_service
from modules.utils.data_utils import clean_data_recursively
from datetime import datetime
from modules.utils.product_family_utils import apply_not_needed_logic_to_row, apply_not_needed_logic_to_data
from config.config import DEFAULT_LLM_MODEL, DEFAULT_PROMPT_TEMPLATE

# For job data, these will eventually come from a dedicated job_helpers.py or similar
# For now, assuming they might be accessible via current_app or direct import if app.py still has them globally
# This will be an issue if they are not globally resolvable or on current_app. 
# Let's assume we will import them from app.py for now. THIS IS A TEMPORARY WORKAROUND.
# from app import _load_job_data, _save_job_data # This creates a circular dependency if app also imports this blueprint.
# A better way is to have job helpers in their own module from the start.
# For now, these routes will FAIL if _load_job_data and _save_job_data are not made available correctly.
# We will address this when creating job_routes by moving job helpers first.

# Placeholder: these functions would be moved to a job_utils.py later
# and then imported here. For now, the routes needing them might not fully work
# until job_utils.py is created and app.py is updated.

import_wizard_bp = Blueprint('import_wizard_bp', __name__)

# Create a simple in-memory cache for request throttling
_request_cache = {}
_cache_expiry = {}

# Global cancellation tracking for bulk operations
_active_bulk_operations = {}
_cancellation_requests = set()

def throttle_requests(timeout=2):
    """
    Decorator to prevent duplicate API calls within a short timeframe.
    """
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            # Create a unique key based on the request data
            request_data = request.get_json()
            if not request_data:
                return f(*args, **kwargs)
            
            # Create a hash of the request data to use as a cache key
            request_str = json.dumps(request_data, sort_keys=True)
            hash_key = hashlib.md5(request_str.encode()).hexdigest()
            
            # Check if we've seen this exact request recently
            current_time = time.time()
            if hash_key in _request_cache:
                last_time = _request_cache[hash_key]
                if current_time - last_time < timeout:
                    current_app.logger.warning(f"Throttled duplicate request: {hash_key} (within {timeout}s)")
                    return jsonify({
                        'success': False, 
                        'message': f'Please wait before submitting identical requests. Try again in {timeout-(current_time-last_time):.1f}s.',
                        'throttled': True
                    }), 429
            
            # Update cache with current time
            _request_cache[hash_key] = current_time
            
            # Clean expired cache entries every 100 requests
            if len(_request_cache) > 100:
                for k in list(_request_cache.keys()):
                    if current_time - _request_cache[k] > timeout:
                        del _request_cache[k]
            
            return f(*args, **kwargs)
        return decorated_function
    return decorator

@import_wizard_bp.route('/import-wizard')
@requires_auth
def import_wizard_page(): # Renamed from import_wizard to avoid conflict
    return render_template('import_wizard.html')

@import_wizard_bp.route('/api/import/cancel-bulk-operation', methods=['POST'])
@requires_auth
def cancel_bulk_operation():
    """Cancel an ongoing bulk operation"""
    try:
        request_data = request.get_json()
        if not request_data:
            return jsonify({'success': False, 'message': 'No data provided'}), 400
        
        job_id = request_data.get('job_id')
        column_name = request_data.get('column_name')
        
        if not job_id:
            return jsonify({'success': False, 'message': 'Job ID required'}), 400
        
        # Create cancellation key
        cancellation_key = f"{job_id}:{column_name}" if column_name else job_id
        
        # Add to cancellation requests
        _cancellation_requests.add(cancellation_key)
        
        current_app.logger.info(f"Cancellation requested for bulk operation: {cancellation_key}")
        
        return jsonify({
            'success': True, 
            'message': 'Cancellation requested',
            'cancellation_key': cancellation_key
        })
        
    except Exception as e:
        current_app.logger.error(f"Error cancelling bulk operation: {str(e)}")
        return jsonify({'success': False, 'message': f'Error: {str(e)}'}), 500

@import_wizard_bp.route('/api/import/upload', methods=['POST'])
@requires_auth
def upload_file():
    redis_job_manager = current_app.redis_job_manager

    try:
        job_id = request.form.get('job_id')
        mapping_file_name = request.form.get('mapping_file', 'Chatgpt_Spaltenerläuterung.xlsx')
        
        # Check if this is a Google Sheets upload
        google_sheet_url = request.form.get('google_sheet_url')
        google_sheet_name = request.form.get('google_sheet_name')

        if not job_id:
            return jsonify({'success': False, 'message': 'No job_id provided'}), 400

        job_metadata = redis_job_manager.get_job_metadata(job_id)
        if not job_metadata:
            return jsonify({'success': False, 'message': f'Job {job_id} not found'}), 404

        processed_data_rows = []
        filename = ""
        
        if google_sheet_url:
            # Process Google Sheet
            from modules.utils.file_processors import process_google_sheet
            try:
                processed_data_rows = process_google_sheet(google_sheet_url, google_sheet_name)
                filename = f"Google Sheet: {google_sheet_url}"
                if google_sheet_name:
                    filename += f" (Sheet: {google_sheet_name})"
            except Exception as e:
                return jsonify({'success': False, 'message': f'Error processing Google Sheet: {str(e)}'}), 400
        else:
            # Process uploaded file
            if 'file' not in request.files:
                return jsonify({'success': False, 'message': 'No file or Google Sheet URL provided'}), 400
            
            file = request.files['file']
            if file.filename == '':
                return jsonify({'success': False, 'message': 'No file selected'}), 400
            
            filename = secure_filename(file.filename)
            file_ext = os.path.splitext(filename)[1].lower()
            
            if file_ext == '.csv':
                processed_data_rows = process_csv_file(file)
            elif file_ext in ['.xlsx', '.xls']:
                # Check if a specific worksheet was requested (for backward compatibility)
                worksheet_name = request.form.get('worksheet_name')
                processed_data_rows = process_excel_file(file, sheet_name=worksheet_name)
            else:
                return jsonify({'success': False, 'message': 'Unsupported file format'}), 400
        
        # Get column mappings (will try Google Sheets first, then fallback to Excel)
        current_mapping_definitions = get_column_mappings(mapping_file_name)
        
        # Create validation configurations for columns that have validation info
        validation_configs = {}
        for mapping in current_mapping_definitions:
            if 'validation' in mapping:
                validation_configs[mapping['column']] = mapping['validation']

        # Store data in Redis using RedisJobDataManager
        from models.redis_models import RedisJobDataManager
        data_manager = RedisJobDataManager(job_id)
        
        # Store the processed data
        if processed_data_rows:
            data_stored = data_manager.store_source_data(processed_data_rows)
            if not data_stored:
                return jsonify({'success': False, 'message': 'Failed to store data in Redis'}), 500

        # Extract source columns
        source_columns = list(processed_data_rows[0].keys()) if processed_data_rows else []
        
        # Update job metadata
        metadata_updates = {
            'name': job_metadata.get('name'),  # Keep existing name
            'original_filename': filename,
            'status': 'data-uploaded',
            'row_count': len(processed_data_rows),
            'column_count': len(source_columns)
        }
        redis_job_manager.update_job_metadata(job_id, metadata_updates)
        
        # Store additional configurations
        redis_job_manager.store_mapping_definitions(job_id, current_mapping_definitions)
        redis_job_manager.store_source_columns(job_id, source_columns)
        redis_job_manager.store_validation_configs(job_id, validation_configs)

        return jsonify({
            'success': True, 'job_id': job_id,
            'message': 'Data uploaded and initial job data saved.',
        })
    
    except Exception as e:
        current_app.logger.error(f"Error processing uploaded data for job: {str(e)}", exc_info=True)
        return jsonify({'success': False, 'message': f'Error processing data: {str(e)}'}), 500

@import_wizard_bp.route('/api/import/ai-map-columns', methods=['POST'])
@requires_auth
def ai_map_columns_route(): # Renamed
    """PERFORMANCE OPTIMIZED: AI column mapping with enhanced caching and error handling"""
    try:
        start_time = time.time()
        request_data = request.get_json()
        if not request_data: return jsonify({'success': False, 'message': 'No data'}), 400

        source_columns = request_data.get('source_columns', [])
        # mapping_columns here are the target definitions from get_column_mappings
        target_mapping_definitions = request_data.get('mapping_columns', [])

        if not source_columns or not target_mapping_definitions:
            return jsonify({'success': False, 'message': 'Source and target mapping definitions required'}), 400

        current_app.logger.info(f"🔍 AI mapping: {len(source_columns)} source columns → {len(target_mapping_definitions)} target columns")

        # PERFORMANCE OPTIMIZATION: Use enhanced AI column mapping with caching
        try:
            from modules.utils.llm_helpers import ai_column_mapping_optimized
            mapped_columns = ai_column_mapping_optimized(source_columns, target_mapping_definitions)
        except (ImportError, AttributeError):
            # Fallback to standard mapping
            from modules.utils.llm_helpers import ai_column_mapping
            mapped_columns = ai_column_mapping(source_columns, target_mapping_definitions)

        processing_time = time.time() - start_time
        current_app.logger.info(f"✅ AI mapping completed in {processing_time:.2f}s: {len(mapped_columns)} mappings found")

        return jsonify({
            'success': True,
            'mapped_columns': mapped_columns,
            'processing_time': round(processing_time, 2),
            'source_count': len(source_columns),
            'target_count': len(target_mapping_definitions),
            'mappings_found': len(mapped_columns)
        })
    except Exception as e:
        current_app.logger.error(f"Error AI column mapping: {str(e)}", exc_info=True)
        return jsonify({'success': False, 'message': f'{str(e)}'}), 500

@import_wizard_bp.route('/api/import/apply-logic', methods=['POST'])
@requires_auth
@throttle_requests(timeout=1)  # Throttle identical requests within 1 second
def apply_logic_route(): # Renamed
    try:
        request_data = request.get_json()
        if not request_data: return jsonify({'success': False, 'message': 'No data'}), 400
        
        job_id = request_data.get('job_id') # Get job_id
        logic = request_data.get('logic', '')
        data_sample = request_data.get('data', []) # Sample data for preview (should be single row for this route)
        target_column_name = request_data.get('column_name', '')  # Get the target column name
        row_index = request_data.get('row_index', None) # Get the row_index if applying to a specific cell
        references = request_data.get('references', {})
        validation_config = request_data.get('validation_config', None)  # Get validation config if provided
        model_name = request_data.get('model_name', None) # Get model_name, will use DEFAULT_LLM_MODEL if None
        
        current_app.logger.info(f"apply_logic_route called for job_id={job_id}, column={target_column_name}, row_index={row_index}")
        
        # Clean the data_sample recursively
        data_sample = clean_data_recursively(data_sample)
        references = clean_data_recursively(references) # Also clean references like notes/prompt if they come from user input
        logic = clean_data_recursively(logic) # Clean logic string itself
        
        if not job_id:
            return jsonify({'success': False, 'message': 'Job ID is required'}), 400
        if not logic or not data_sample:
            return jsonify({'success': False, 'message': 'Logic and data sample required'}), 400
        if not target_column_name:
            return jsonify({'success': False, 'message': 'Target column name is required'}), 400
        if len(data_sample) != 1:
            # This route is intended for a single row sample (preview) or applying to one cell at a time.
            # The sample_data here represents the row context for the transformation.
            current_app.logger.warning(f"apply_logic_route received {len(data_sample)} rows, using only the first.")
            # For saving, we need the specific row_index it applies to in the full dataset.

        redis_job_manager = current_app.redis_job_manager
        job_metadata = redis_job_manager.get_job_metadata(job_id)
        if not job_metadata:
            return jsonify({'success': False, 'message': f'Job {job_id} not found'}), 404

        # Include the target column name in the references to aid in worksheet detection
        references['target_column'] = target_column_name

        # Add additional data parameters for new placeholders
        # For now, we'll use the validation config worksheet as the additional data source
        # This can be made configurable later if needed
        if validation_config and 'worksheet' in validation_config and 'column' in validation_config:
            references['additional_data_worksheet'] = validation_config['worksheet']
            references['additional_data_validation_column'] = validation_config['column']

        # If validation config is provided, get validation data
        if validation_config and 'worksheet' in validation_config and 'column' in validation_config:
            validation_configs = redis_job_manager.get_validation_configs(job_id)
            if target_column_name in validation_configs:
                from modules.google_sheets_service import google_sheets_service
                
                if google_sheets_service.is_authenticated():
                    validation_data = google_sheets_service.get_validation_data(
                        validation_config['worksheet'], 
                        validation_config['column']
                    )
                else:
                    # Fallback to Excel file if Google Sheets not available
                    mapping_file_name = 'Chatgpt_Spaltenerläuterung.xlsx'
                    mapping_file_path = os.path.join(current_app.root_path, mapping_file_name)
                    from modules.utils.llm_helpers import get_validation_data
                    validation_data = get_validation_data(
                        mapping_file_path, 
                        validation_config['worksheet'], 
                        validation_config['column']
                    )
                references['validation_data'] = validation_data
        
        # Generate the preview prompts first (these will be included in the response)
        from modules.utils.llm_helpers import generate_prompts_for_logic
        system_prompt, user_prompt = generate_prompts_for_logic(
            logic, 
            data_sample, 
            received_references=references,
            model_name=model_name
        )
        
        # apply_logic_transformation expects a list of samples, so pass the single sample in a list
        llm_result = apply_logic_transformation(logic, data_sample, references, model_name)
        
        # Get all transformed values from the LLM result
        all_transformed_values = llm_result.get('transformed_values', []) # Get all transformed values
        rendered_logic_strings = llm_result.get('rendered_logic_strings', [logic] * len(data_sample))
        raw_llm_responses = llm_result.get('raw_llm_responses', [None] * len(data_sample))
        call_id_for_transform = llm_result.get('call_id') # Get the call_id

        # If row_index is provided, it means we are applying this logic to a specific cell for persistence.
        if row_index is not None and isinstance(row_index, int) and row_index >= 0:
            if all_transformed_values and len(all_transformed_values) > 0: # If transformation returned values
                # Update the cell directly in Redis
                from models.redis_models import RedisJobDataManager, RedisTransformationManager
                data_manager = RedisJobDataManager(job_id)
                transform_manager = RedisTransformationManager(job_id)
                
                result_value = all_transformed_values[0]
                
                # Update the cell
                data_manager.update_cell(row_index, target_column_name, result_value)
                
                # Store transformation details for tracking
                transformation_entry = {
                    'row_index': row_index,
                    'original_value': data_sample[0].get(target_column_name) if data_sample else None,
                    'transformed_value': result_value,
                    'manual_edit': False,
                    'logic_applied': logic,
                    'rendered_logic': rendered_logic_strings[0] if rendered_logic_strings else logic,
                    'raw_response': raw_llm_responses[0] if raw_llm_responses else None,
                    'system_prompt': system_prompt,
                    'user_prompt': user_prompt,
                    'model_used': model_name,
                    'timestamp': datetime.now().isoformat(),
                    'call_id': call_id_for_transform
                }
                
                # Get existing transformations and update
                existing_transformations = transform_manager.get_column_transformations(target_column_name)
                
                # Ensure existing_transformations is a list of dictionaries
                if not isinstance(existing_transformations, list):
                    current_app.logger.warning(f"Invalid transformations data type: {type(existing_transformations)}, resetting to empty list")
                    existing_transformations = []
                
                # Clean up any non-dict entries from existing transformations
                cleaned_transformations = []
                for trans in existing_transformations:
                    if isinstance(trans, dict):
                        cleaned_transformations.append(trans)
                    else:
                        current_app.logger.warning(f"Skipping invalid transformation entry: {type(trans)} - {trans}")
                existing_transformations = cleaned_transformations
                
                # Update or add the transformation for this row
                updated = False
                for i, trans in enumerate(existing_transformations):
                    if isinstance(trans, dict) and trans.get('row_index') == row_index:
                        existing_transformations[i] = transformation_entry
                        updated = True
                        break
                
                if not updated:
                    existing_transformations.append(transformation_entry)
                
                # Store the updated transformations
                transform_manager.store_column_transformations(target_column_name, existing_transformations)
        
        # Log the logic run to Google Sheets if row_index is provided (actual application)
        if row_index is not None and all_transformed_values:
            try:
                from modules.utils.logic_prompt_logger import log_logic_run
                user_notes = references.get('notes', '')
                success_status = 'Success' if all_transformed_values and not str(all_transformed_values[0]).startswith('Error:') else 'Error'
                error_details = str(all_transformed_values[0]) if str(all_transformed_values[0]).startswith('Error:') else None
                
                log_logic_run(
                    target_column=target_column_name,
                    logic_template=logic,
                    model_used=model_name,
                    row_count=1,  # Single row application
                    job_id=job_id,
                    user_notes=user_notes,
                    success_status=success_status,
                    error_details=error_details
                )
            except Exception as log_error:
                current_app.logger.warning(f"Failed to log logic run: {str(log_error)}")
        
        # Return results
        response_data = {
            'success': True,
            'transformed_values': all_transformed_values,
            'transformed_data': all_transformed_values,  # Add this field for frontend compatibility
            'rendered_logic_strings': rendered_logic_strings,
            'raw_llm_responses': raw_llm_responses,
            'preview': {
                'system_prompt': system_prompt,
                'user_prompt': user_prompt
            },
            'prompts': {
                'system': system_prompt,
                'user': user_prompt,
                'model': model_name
            }
        }
        
        # Log the response for debugging
        current_app.logger.info(f"Returning response: {json.dumps(response_data)}")
        
        return jsonify(response_data)
    except Exception as e:
        current_app.logger.error(f"Error applying logic: {str(e)}", exc_info=True)
        return jsonify({'success': False, 'message': f'{str(e)}'}), 500

@import_wizard_bp.route('/api/import/bulk-apply-logic', methods=['POST'])
@requires_auth
def bulk_apply_logic_route():
    try:
        request_data = request.get_json()
        if not request_data: return jsonify({'success': False, 'message': 'No data'}), 400
        
        job_id = request_data.get('job_id')
        logic = request_data.get('logic', '')
        target_column_name = request_data.get('column_name', '')
        
        # Parameters for batching unfilled rows (original behavior)
        batch_size_param = request_data.get('batch_size', 10)
        offset_param = request_data.get('offset', 0)

        # NEW: Parameters for user-limited requests
        user_requested_limit = request_data.get('user_requested_limit', False)
        max_total_rows = request_data.get('max_total_rows', None)

        # Parameters for error_rows_only_mode
        error_rows_only_mode = request_data.get('error_rows_only_mode', False)
        specific_row_data = request_data.get('data', None) 
        specific_row_indices = request_data.get('row_indices', None)
        
        references = request_data.get('references', {})
        validation_config = request_data.get('validation_config', None)
        model_name = request_data.get('model_name', None)
        
        # PERFORMANCE OPTIMIZATION: Adjust batch size for large datasets
        original_batch_size = batch_size_param
        if user_requested_limit and max_total_rows and max_total_rows > 1000:
            batch_size_param = min(batch_size_param, 5)  # Smaller batches for large datasets
            current_app.logger.info(f"Large dataset detected, reducing batch size from {original_batch_size} to {batch_size_param}")
        
        current_app.logger.info(f"Bulk apply logic called for job {job_id}, column {target_column_name}. Mode: {'ErrorRowsOnly' if error_rows_only_mode else ('UserLimited' if user_requested_limit else 'BatchUnfilled')}")
        if error_rows_only_mode:
            current_app.logger.info(f"ErrorRowsOnly mode: processing {len(specific_row_indices) if specific_row_indices else 0} specific rows.")
        elif user_requested_limit and max_total_rows:
            current_app.logger.info(f"UserLimited mode: max {max_total_rows} rows requested, batch_size: {batch_size_param}")

        # Clean inputs
        references = clean_data_recursively(references) 
        logic = clean_data_recursively(logic)
        
        if not job_id:
            return jsonify({'success': False, 'message': 'Job ID is required'}), 400
        if not logic:
            return jsonify({'success': False, 'message': 'Logic is required'}), 400
        if not target_column_name:
            return jsonify({'success': False, 'message': 'Target column name is required'}), 400
        
        redis_job_manager = current_app.redis_job_manager
        job_metadata = redis_job_manager.get_job_metadata(job_id)
        if not job_metadata:
            return jsonify({'success': False, 'message': f'Job {job_id} not found'}), 404
            
        # Get basic job info
        from models.redis_models import RedisJobDataManager
        data_manager = RedisJobDataManager(job_id)
        total_rows = job_metadata.get('row_count', 0)
        
        if total_rows == 0 and not error_rows_only_mode:
            return jsonify({'success': False, 'message': 'No source data found in job'}), 400
        
        references['target_column'] = target_column_name
        if validation_config and 'worksheet' in validation_config and 'column' in validation_config:
            references['additional_data_worksheet'] = validation_config['worksheet']
            references['additional_data_validation_column'] = validation_config['column']

        actual_batch_data_to_process = []
        actual_batch_original_indices = []
        next_offset_to_return = -1
        progress_for_unfilled_mode = 0
        done_for_unfilled_mode = False

        if error_rows_only_mode:
            if not specific_row_data or specific_row_indices is None:
                return jsonify({'success': False, 'message': 'In error_rows_only_mode, specific row data and indices are required.'}), 400
            actual_batch_data_to_process = clean_data_recursively(specific_row_data)
            actual_batch_original_indices = specific_row_indices
            current_app.logger.info(f"Processing {len(actual_batch_original_indices)} rows in error_rows_only_mode.")
        else:
            # Logic for batching unfilled rows (with optional user limits)
            batch_size = int(batch_size_param)
            offset = int(offset_param)
            current_original_idx = offset 
            processed_in_this_pass = 0

        # OPTIMIZATION: Redirect to the dedicated Redis-optimized bulk apply logic endpoint
        # This endpoint in import_jobs_routes.py has been specifically designed for Redis storage
        current_app.logger.info(f"🔄 Redirecting bulk apply logic to Redis-optimized endpoint for job {job_id}")
        
        # Forward the request to the Redis-optimized endpoint
        from flask import url_for
        import requests
        
        # Prepare the request payload for the Redis endpoint
        # Convert offset-based pagination to page-based pagination (Redis uses 1-based pages)
        page_number = (offset_param // batch_size_param) + 1 if batch_size_param > 0 else 1
        
        redis_payload = {
            'logic': logic,
            'column_name': target_column_name,
            'references': references,
            'validation_config': validation_config,
            'model_name': model_name,
            'batch_size': batch_size_param,
            'page': page_number
        }
        
        try:
            # Make internal request to the Redis endpoint
            redis_url = f"http://127.0.0.1:{request.environ.get('SERVER_PORT', '5001')}/api/import/jobs/{job_id}/bulk-apply-logic"
            redis_response = requests.post(
                redis_url,
                json=redis_payload,
                headers={'Authorization': request.headers.get('Authorization', '')}
            )
            
            if redis_response.status_code == 200:
                redis_data = redis_response.json()
                
                # Convert Redis response format to legacy format expected by frontend
                legacy_response = {
                    'success': redis_data.get('success', True),
                    'message': redis_data.get('message', 'Processing completed'),
                    'results': [],
                    'progress': redis_data.get('progress', 0),
                    'done': redis_data.get('done', False),
                    'total_rows': total_rows,
                    'next_offset': redis_data.get('next_page', -1) * batch_size_param if redis_data.get('next_page', -1) > 0 else -1
                }
                
                # Convert processed rows to legacy format
                for result in redis_data.get('results', []):
                    legacy_response['results'].append({
                        'row_index': result.get('row_index'),
                        'value': result.get('value'),
                        'isError': result.get('isError', False),
                        'transformedValue': result.get('value'),  # Legacy compatibility
                        'rowIndex': result.get('row_index')  # Legacy compatibility
                    })
                
                return jsonify(legacy_response)
            else:
                # Fallback to error response
                current_app.logger.error(f"Redis endpoint failed with status {redis_response.status_code}")
                return jsonify({
                    'success': False,
                    'message': f'Redis bulk processing failed: {redis_response.text}',
                    'progress': 0,
                    'done': False,
                    'total_rows': total_rows,
                    'results': []
                }), 500
                
        except Exception as e:
            current_app.logger.error(f"Error calling Redis bulk endpoint: {str(e)}")
            return jsonify({
                'success': False,
                'message': f'Failed to process bulk logic: {str(e)}',
                'progress': 0,
                'done': False,
                'total_rows': total_rows,
                'results': []
            }), 500
            
    except Exception as e:
        current_app.logger.error(f"Error in bulk apply logic: {str(e)}")
        return jsonify({'success': False, 'message': f'Error: {str(e)}'}), 500

@import_wizard_bp.route('/api/import/export', methods=['POST'])
@requires_auth
def export_data_route(): # Renamed
    try:
        request_data = request.get_json()
        if not request_data or 'data' not in request_data:
            return jsonify({'success': False, 'message': 'No data to export'}), 400
        
        df = pd.DataFrame(request_data['data'])
        csv_data_io = io.StringIO()
        df.to_csv(csv_data_io, index=False)
        csv_content = csv_data_io.getvalue()
        csv_data_io.close()
        
        response = make_response(csv_content)
        response.headers['Content-Disposition'] = 'attachment; filename=exported_data.csv'
        response.headers['Content-Type'] = 'text/csv'
        return response
    except Exception as e:
        current_app.logger.error(f"Error exporting data: {str(e)}", exc_info=True)
        return jsonify({'success': False, 'message': f'{str(e)}'}), 500

@import_wizard_bp.route('/api/import/get-validation-data', methods=['POST'])
@requires_auth
def get_validation_data_route():
    try:
        request_data = request.get_json()
        if not request_data: return jsonify({'success': False, 'message': 'No data'}), 400
        
        column_name = request_data.get('column_name', '')
        job_id = request_data.get('job_id', '')
        
        if not column_name or not job_id:
            return jsonify({'success': False, 'message': 'Column name and job ID required'}), 400
        
        # Get job metadata and validation configs from Redis
        redis_job_manager = current_app.redis_job_manager
        job_metadata = redis_job_manager.get_job_metadata(job_id)
        
        if not job_metadata:
            return jsonify({'success': False, 'message': f'Job {job_id} not found'}), 404
        
        # Check if validation config exists for this column
        validation_configs = redis_job_manager.get_validation_configs(job_id)
        if column_name not in validation_configs:
            return jsonify({'success': False, 'message': f'No validation config for column {column_name}'}), 404
        
        validation_config = validation_configs[column_name]
        
        # Get validation data from Google Sheets
        from modules.google_sheets_service import google_sheets_service
        
        if google_sheets_service.is_authenticated():
            validation_data = google_sheets_service.get_validation_data(
                validation_config['worksheet'], 
                validation_config['column']
            )
        else:
            # Fallback to Excel file if Google Sheets not available
            mapping_file_name = 'Chatgpt_Spaltenerläuterung.xlsx'
            mapping_file_path = os.path.join(current_app.root_path, mapping_file_name)
            from modules.utils.llm_helpers import get_validation_data
            validation_data = get_validation_data(
                mapping_file_path, 
                validation_config['worksheet'], 
                validation_config['column']
            )
        
        if not validation_data:
            return jsonify({'success': False, 'message': 'Failed to retrieve validation data'}), 500
        
        return jsonify({
            'success': True,
            'validation_data': validation_data,
            'validation_config': validation_config
        })
    except Exception as e:
        current_app.logger.error(f"Error getting validation data: {str(e)}", exc_info=True)
        return jsonify({'success': False, 'message': f'{str(e)}'}), 500

@import_wizard_bp.route('/api/import/update-cell', methods=['POST'])
@requires_auth
def update_cell_route():
    try:
        request_data = request.get_json()
        if not request_data: return jsonify({'success': False, 'message': 'No data'}), 400
        
        job_id = request_data.get('job_id', '')
        row_index = request_data.get('row_index', -1)
        column_name = request_data.get('column_name', '')
        new_value = request_data.get('new_value', '')
        validate = request_data.get('validate', True)
        
        if not job_id or row_index < 0 or not column_name:
            return jsonify({'success': False, 'message': 'Job ID, row index, and column name are required'}), 400
        
        # Check if job exists using Redis
        redis_job_manager = current_app.redis_job_manager
        job_metadata = redis_job_manager.get_job_metadata(job_id)
        if not job_metadata: 
            return jsonify({'success': False, 'message': f'Job {job_id} not found'}), 404
        
        # Validate against validation rules if needed
        if validate:
            validation_configs = redis_job_manager.get_validation_configs(job_id)
            if column_name in validation_configs:
                validation_config = validation_configs[column_name]
                
                # Get validation data from Google Sheets
                from modules.google_sheets_service import google_sheets_service
                
                if google_sheets_service.is_authenticated():
                    validation_data = google_sheets_service.get_validation_data(
                        validation_config['worksheet'], 
                        validation_config['column']
                    )
                else:
                    # Fallback to Excel file if Google Sheets not available
                    mapping_file_name = 'Chatgpt_Spaltenerläuterung.xlsx'
                    mapping_file_path = os.path.join(current_app.root_path, mapping_file_name)
                    from modules.utils.llm_helpers import get_validation_data
                    validation_data = get_validation_data(
                        mapping_file_path, 
                        validation_config['worksheet'], 
                        validation_config['column']
                    )
                    
                if new_value not in validation_data:
                    return jsonify({
                        'success': False, 
                        'message': f'Value "{new_value}" is not in the validation list for column {column_name}',
                        'validation_data': validation_data
                    }), 400
        
        # Update the cell directly in Redis using RedisJobDataManager
        from models.redis_models import RedisJobDataManager
        data_manager = RedisJobDataManager(job_id)
        
        # Update the original data cell
        cell_updated = data_manager.update_cell(row_index, column_name, new_value)
        
        if cell_updated:
            # Also store as a transformation result for tracking
            from models.redis_models import RedisTransformationManager
            transform_manager = RedisTransformationManager(job_id)
            
            transformation_entry = {
                'row_index': row_index,
                'original_value': None,  # We don't have the original value here
                'transformed_value': new_value,
                'manual_edit': True,  # Flag to indicate this was manually edited
                'timestamp': datetime.now().isoformat()
            }
            
            # Get existing transformations for this column
            existing_transformations = transform_manager.get_column_transformations(column_name)
            
            # Ensure existing_transformations is a list of dictionaries
            if not isinstance(existing_transformations, list):
                current_app.logger.warning(f"Invalid transformations data type: {type(existing_transformations)}, resetting to empty list")
                existing_transformations = []
            
            # Clean up any non-dict entries from existing transformations
            cleaned_transformations = []
            for trans in existing_transformations:
                if isinstance(trans, dict):
                    cleaned_transformations.append(trans)
                else:
                    current_app.logger.warning(f"Skipping invalid transformation entry: {type(trans)} - {trans}")
            existing_transformations = cleaned_transformations
            
            # Update or add the transformation for this row
            updated = False
            for i, trans in enumerate(existing_transformations):
                if isinstance(trans, dict) and trans.get('row_index') == row_index:
                    existing_transformations[i] = transformation_entry
                    updated = True
                    break
            
            if not updated:
                existing_transformations.append(transformation_entry)
            
            # Store the updated transformations
            transform_manager.store_column_transformations(column_name, existing_transformations)
            
            return jsonify({'success': True, 'message': 'Cell updated successfully'})
        else:
            return jsonify({'success': False, 'message': 'Failed to update cell in Redis'}), 500
            
    except Exception as e:
        current_app.logger.error(f"Error updating cell: {str(e)}", exc_info=True)
        return jsonify({'success': False, 'message': f'{str(e)}'}), 500

@import_wizard_bp.route('/api/import/preview-prompt', methods=['POST'])
@requires_auth
def preview_prompt_route():
    try:
        request_data = request.get_json()
        if not request_data:
            return jsonify({'error': 'No data provided'}), 400

        # Extract data from the request
        logic = request_data.get('logic', '')
        row_data = request_data.get('rowData', [])
        references = request_data.get('references', {})
        model_name = request_data.get('model', None) # Will use DEFAULT_LLM_MODEL if None
        target_column = request_data.get('column_name', '')
        
        current_app.logger.info(f"preview_prompt_route called with logic: {logic[:50]}... and {len(row_data)} rows")

        if not logic:
            return jsonify({'error': 'No logic provided'}), 400
        
        if not row_data:
            row_data = [{}]  # Provide an empty row if none specified
            
        # Clean the data to ensure valid input
        row_data = clean_data_recursively(row_data)
        references = clean_data_recursively(references)
        logic = clean_data_recursively(logic)
        
        # Add target column name to references for worksheet detection
        if target_column:
            references['target_column'] = target_column
            current_app.logger.info(f"DEBUG: target_column set to: {target_column}")
            
            # For preview, we can try to get validation config from mapping definitions
            # to populate additional data parameters
            try:
                from modules.google_sheets_service import google_sheets_service
                if google_sheets_service.is_authenticated():
                    mappings = google_sheets_service.read_mapping_definitions()
                    current_app.logger.info(f"DEBUG: Found {len(mappings)} mappings")
                    for mapping in mappings:
                        current_app.logger.info(f"DEBUG: Checking mapping for column: {mapping.get('column')}")
                        if mapping.get('column') == target_column and mapping.get('validation'):
                            validation_config = mapping['validation']
                            current_app.logger.info(f"DEBUG: Found validation config: {validation_config}")
                            if 'worksheet' in validation_config and 'column' in validation_config:
                                references['additional_data_worksheet'] = validation_config['worksheet']
                                references['additional_data_validation_column'] = validation_config['column']
                                current_app.logger.info(f"DEBUG: Set additional_data_validation_column to: {validation_config['column']}")
                                break
                    else:
                        current_app.logger.info(f"DEBUG: No validation config found for target_column: {target_column}")
            except Exception as e:
                current_app.logger.warning(f"Could not load validation config for preview: {str(e)}")
        else:
            current_app.logger.info("DEBUG: No target_column provided")
            
        # Generate the prompts using the logic and data
        system_prompt, user_prompt = generate_prompts_for_logic(
            logic, 
            row_data, 
            received_references=references,
            model_name=model_name
        )
        
        return jsonify({
            'success': True,
            'system_prompt': system_prompt,
            'user_prompt': user_prompt
        })
        
    except Exception as e:
        current_app.logger.error(f"Error generating prompt preview: {str(e)}", exc_info=True)
        return jsonify({'success': False, 'error': f'Error generating prompt preview: {str(e)}'}), 500

@import_wizard_bp.route('/api/import/get-worksheet-data', methods=['POST'])
@requires_auth
def get_worksheet_data_route():
    try:
        request_data = request.get_json()
        if not request_data:
            return jsonify({'success': False, 'message': 'No data provided'}), 400

        worksheet_name = request_data.get('worksheet_name', '')
        
        if not worksheet_name:
            return jsonify({'success': False, 'message': 'Worksheet name is required'}), 400
        
        # First try Google Sheets
        from modules.google_sheets_service import google_sheets_service
        
        if google_sheets_service.is_authenticated():
            try:
                worksheet_df = google_sheets_service.get_worksheet_data_as_dataframe(worksheet_name)
                if worksheet_df is not None:
                    # Convert the DataFrame to a list of lists (2D array)
                    data = worksheet_df.replace({pd.NA: None, '': None}).values.tolist()
                    
                    # Add column headers as the first row
                    headers = worksheet_df.columns.tolist()
                    data.insert(0, headers)
                    
                    # Generate markdown table
                    markdown_table = ""
                    if headers:
                        markdown_table += "| " + " | ".join(str(h) for h in headers) + " |\n"
                        markdown_table += "|" + "|".join(["---" for _ in headers]) + "|\n"
                        for row in data[1:]:
                            markdown_table += "| " + " | ".join(str(cell) if cell is not None else "" for cell in row) + " |\n"
                    
                    return jsonify({
                        'success': True,
                        'data': data,
                        'markdown_table': markdown_table,
                        'worksheet_name': worksheet_name,
                        'source': 'google_sheets'
                    })
            except Exception as e:
                current_app.logger.warning(f"Failed to load from Google Sheets, falling back to Excel: {str(e)}")
        
        # Fallback to Excel file
        mapping_file_name = 'Chatgpt_Spaltenerläuterung.xlsx'
        mapping_file_path = os.path.join(current_app.root_path, mapping_file_name)
        
        if not os.path.exists(mapping_file_path):
            current_app.logger.error(f"Mapping file not found: {mapping_file_path}")
            return jsonify({'success': False, 'message': f'Mapping file not found: {mapping_file_name}'}), 404
            
        try:
            worksheet_df = pd.read_excel(mapping_file_path, sheet_name=worksheet_name)
            data = worksheet_df.replace({pd.NA: None, float('nan'): None}).values.tolist()
            headers = worksheet_df.columns.tolist()
            data.insert(0, headers)
            
            markdown_table = ""
            if headers:
                markdown_table += "| " + " | ".join(str(h) for h in headers) + " |\n"
                markdown_table += "|" + "|".join(["---" for _ in headers]) + "|\n"
                for row in data[1:]:
                    markdown_table += "| " + " | ".join(str(cell) if cell is not None else "" for cell in row) + " |\n"
            
            return jsonify({
                'success': True,
                'data': data,
                'markdown_table': markdown_table,
                'worksheet_name': worksheet_name,
                'source': 'excel'
            })
            
        except Exception as e:
            current_app.logger.error(f"Error reading worksheet {worksheet_name}: {str(e)}", exc_info=True)
            return jsonify({'success': False, 'message': f'Error reading worksheet: {str(e)}'}), 500
            
    except Exception as e:
        current_app.logger.error(f"Error getting worksheet data: {str(e)}", exc_info=True)
        return jsonify({'success': False, 'message': f'Error: {str(e)}'}), 500

@import_wizard_bp.route('/api/import/get-google-sheet-names', methods=['POST'])
@requires_auth
def get_google_sheet_names():
    """Get sheet names from a Google Spreadsheet."""
    try:
        request_data = request.get_json()
        if not request_data:
            return jsonify({'success': False, 'message': 'No data provided'}), 400
        
        sheet_url = request_data.get('sheet_url')
        if not sheet_url:
            return jsonify({'success': False, 'message': 'Sheet URL is required'}), 400
        
        from modules.utils.file_processors import get_google_sheet_names
        
        sheet_names = get_google_sheet_names(sheet_url)
        
        return jsonify({
            'success': True,
            'sheet_names': sheet_names,
            'message': f'Found {len(sheet_names)} sheets'
        })
        
    except Exception as e:
        current_app.logger.error(f"Error getting Google Sheet names: {str(e)}", exc_info=True)
        return jsonify({'success': False, 'message': f'Error: {str(e)}'}), 500

@import_wizard_bp.route('/api/import/get-excel-sheet-names', methods=['POST'])
@requires_auth
def get_excel_sheet_names():
    """Get sheet names from an uploaded Excel file."""
    try:
        if 'file' not in request.files:
            return jsonify({'success': False, 'message': 'No file provided'}), 400
        
        file = request.files['file']
        if file.filename == '':
            return jsonify({'success': False, 'message': 'No file selected'}), 400
        
        filename = secure_filename(file.filename)
        file_ext = os.path.splitext(filename)[1].lower()
        
        if file_ext not in ['.xlsx', '.xls']:
            return jsonify({'success': False, 'message': 'Please upload an Excel file (.xlsx or .xls)'}), 400
        
        from modules.utils.file_processors import get_excel_sheet_names
        
        sheet_names = get_excel_sheet_names(file)
        
        return jsonify({
            'success': True,
            'sheet_names': sheet_names,
            'message': f'Found {len(sheet_names)} sheets'
        })
        
    except Exception as e:
        current_app.logger.error(f"Error getting Excel sheet names: {str(e)}", exc_info=True)
        return jsonify({'success': False, 'message': f'Error: {str(e)}'}), 500

@import_wizard_bp.route('/api/import/upload-with-worksheet', methods=['POST'])
@requires_auth
def upload_file_with_worksheet():
    """Upload and process Excel file with specific worksheet selection using Redis storage."""
    from models.redis_models import RedisJobManager, RedisJobDataManager
    
    try:
        job_id = request.form.get('job_id')
        mapping_file_name = request.form.get('mapping_file', 'Chatgpt_Spaltenerläuterung.xlsx')
        selected_worksheet = request.form.get('selected_worksheet')
        
        if not job_id:
            return jsonify({'success': False, 'message': 'No job_id provided'}), 400

        # Use Redis job manager
        redis_job_manager = RedisJobManager()
        job_metadata = redis_job_manager.get_job_metadata(job_id)
        if not job_metadata:
            return jsonify({'success': False, 'message': f'Job {job_id} not found'}), 404

        if 'file' not in request.files:
            return jsonify({'success': False, 'message': 'No file provided'}), 400
        
        file = request.files['file']
        if file.filename == '':
            return jsonify({'success': False, 'message': 'No file selected'}), 400
        
        filename = secure_filename(file.filename)
        file_ext = os.path.splitext(filename)[1].lower()
        
        if file_ext not in ['.xlsx', '.xls']:
            return jsonify({'success': False, 'message': 'Please upload an Excel file (.xlsx or .xls)'}), 400
        
        from modules.utils.file_processors import process_excel_file
        
        # Process the Excel file with the selected worksheet
        processed_data_rows = process_excel_file(file, sheet_name=selected_worksheet)
        
        # Update filename to include worksheet info
        if selected_worksheet:
            filename = f"{filename} (Sheet: {selected_worksheet})"
        
        # Get column mappings (will try Google Sheets first, then fallback to Excel)
        current_mapping_definitions = get_column_mappings(mapping_file_name)
        
        # Create validation configurations for columns that have validation info
        validation_configs = {}
        for mapping in current_mapping_definitions:
            if 'validation' in mapping:
                validation_configs[mapping['column']] = mapping['validation']

        # Store data in Redis
        redis_data_manager = RedisJobDataManager(job_id)
        data_stored = redis_data_manager.store_source_data(processed_data_rows)
        
        if not data_stored:
            return jsonify({'success': False, 'message': 'Failed to store data in Redis'}), 500

        # Store mapping definitions and validation configs in Redis
        mapping_stored = redis_job_manager.store_mapping_definitions(job_id, current_mapping_definitions)
        source_columns = list(processed_data_rows[0].keys()) if processed_data_rows else []
        columns_stored = redis_job_manager.store_source_columns(job_id, source_columns)
        validation_stored = redis_job_manager.store_validation_configs(job_id, validation_configs)
        
        if not mapping_stored or not columns_stored or not validation_stored:
            current_app.logger.warning(f"Some configurations failed to store in Redis for job {job_id}")

        # Update job metadata in Redis
        job_updates = {
            'original_filename': filename,
            'status': 'data-uploaded',
            'row_count': len(processed_data_rows),
            'column_count': len(source_columns)
        }
        
        metadata_updated = redis_job_manager.update_job_metadata(job_id, job_updates)
        
        if not metadata_updated:
            return jsonify({'success': False, 'message': 'Failed to update job metadata'}), 500

        current_app.logger.info(f"Successfully uploaded {len(processed_data_rows)} rows from worksheet '{selected_worksheet}' to Redis job {job_id}")
        
        return jsonify({
            'success': True, 'job_id': job_id,
            'message': f'Data uploaded from worksheet "{selected_worksheet}" and saved to Redis.',
            'worksheet_name': selected_worksheet,
            'rows_uploaded': len(processed_data_rows)
        })
    
    except Exception as e:
        current_app.logger.error(f"Error processing uploaded Excel file with worksheet: {str(e)}", exc_info=True)
        return jsonify({'success': False, 'message': f'Error processing data: {str(e)}'}), 500

@import_wizard_bp.route('/api/import/log-mapping-save', methods=['POST'])
@requires_auth
def log_mapping_save_route():
    """Log a mapping save action to Google Sheets."""
    try:
        request_data = request.get_json()
        if not request_data:
            return jsonify({'success': False, 'message': 'No data provided'}), 400
        
        target_column = request_data.get('target_column', '')
        mapping_type = request_data.get('mapping_type', '')
        source_column = request_data.get('source_column', '')
        string_value = request_data.get('string_value', '')
        logic_template = request_data.get('logic_template', '')
        model_used = request_data.get('model_used', '')
        job_id = request_data.get('job_id', '')
        user_notes = request_data.get('user_notes', '')
        
        if not target_column or not mapping_type:
            return jsonify({'success': False, 'message': 'Target column and mapping type are required'}), 400
        
        # Log the mapping save
        from modules.utils.logic_prompt_logger import log_mapping_save
        success = log_mapping_save(
            target_column=target_column,
            mapping_type=mapping_type,
            source_column=source_column if source_column else None,
            string_value=string_value if string_value else None,
            logic_template=logic_template if logic_template else None,
            model_used=model_used if model_used else None,
            job_id=job_id if job_id else None,
            user_notes=user_notes if user_notes else None
        )
        
        if success:
            return jsonify({'success': True, 'message': 'Mapping save logged successfully'})
        else:
            return jsonify({'success': False, 'message': 'Failed to log mapping save'})
            
    except Exception as e:
        current_app.logger.error(f"Error logging mapping save: {str(e)}", exc_info=True)
        return jsonify({'success': False, 'message': f'Error: {str(e)}'}), 500

@import_wizard_bp.route('/api/import/export-excel', methods=['POST'])
@requires_auth
def export_job_excel():
    """Export transformed data as Excel file - redirects to Redis-optimized endpoint."""
    try:
        request_data = request.get_json()
        
        if not request_data:
            return jsonify({'success': False, 'message': 'No data provided'}), 400
            
        job_id = request_data.get('job_id')
        if not job_id:
            return jsonify({'success': False, 'message': 'Job ID required'}), 400
        
        # OPTIMIZATION: Redirect to Redis-optimized export endpoint
        current_app.logger.info(f"🔄 Redirecting Excel export to Redis-optimized endpoint for job {job_id}")
        
        # Forward to the Redis endpoint using internal redirect
        import requests
        
        try:
            # Make internal request to the Redis export endpoint
            redis_url = f"http://127.0.0.1:{request.environ.get('SERVER_PORT', '5001')}/api/import/jobs/{job_id}/export/excel"
            redis_response = requests.get(
                redis_url,
                headers={'Authorization': request.headers.get('Authorization', '')}
            )
            
            if redis_response.status_code == 200:
                # Forward the file response
                response = make_response(redis_response.content)
                
                # Copy headers from Redis response
                for header_name, header_value in redis_response.headers.items():
                    if header_name.lower() in ['content-type', 'content-disposition']:
                        response.headers[header_name] = header_value
                
                return response
            else:
                current_app.logger.error(f"Redis export endpoint failed with status {redis_response.status_code}")
                return jsonify({
                    'success': False,
                    'message': f'Export failed: {redis_response.text}'
                }), redis_response.status_code
                
        except Exception as e:
            current_app.logger.error(f"Error calling Redis export endpoint: {str(e)}")
            return jsonify({
                'success': False,
                'message': f'Failed to export: {str(e)}'
            }), 500
        
    except Exception as e:
        current_app.logger.error(f"Error in export route: {str(e)}", exc_info=True)
        return jsonify({'success': False, 'message': f'Export failed: {str(e)}'}), 500

@import_wizard_bp.route('/api/import/import-json', methods=['POST'])
@requires_auth
def import_job_json():
    """Import JSON file with complete job data (replaces Excel import)."""
    try:
        job_manager = current_app.job_manager
        
        if 'file' not in request.files:
            return jsonify({'success': False, 'message': 'No file provided'}), 400
        
        file = request.files['file']
        if file.filename == '':
            return jsonify({'success': False, 'message': 'No file selected'}), 400
            
        # Check if it's a JSON file
        filename = secure_filename(file.filename)
        file_ext = os.path.splitext(filename)[1].lower()
        
        if file_ext != '.json':
            return jsonify({'success': False, 'message': 'Only JSON files are supported for import. Please upload a jobs_xxx.json file.'}), 400
            
        job_id = request.form.get('job_id')
        if not job_id:
            return jsonify({'success': False, 'message': 'Job ID required'}), 400
            
        job_data = job_manager.load_job_data(job_id)
        if not job_data:
            return jsonify({'success': False, 'message': 'Job not found'}), 404
            
        # Parse JSON file
        try:
            json_content = json.loads(file.read().decode('utf-8'))
        except json.JSONDecodeError as e:
            return jsonify({'success': False, 'message': f'Invalid JSON file: {str(e)}'}), 400
        except UnicodeDecodeError as e:
            return jsonify({'success': False, 'message': f'File encoding error: {str(e)}'}), 400
            
        # Validate that this is a job file (basic check)
        if 'job_id' not in json_content:
            return jsonify({'success': False, 'message': 'This doesn\'t appear to be a valid job JSON file'}), 400
            
        # Merge imported data with current job data (preserving job_id)
        original_job_id = job_data['job_id']
        imported_data = json_content
        imported_data['job_id'] = original_job_id  # Keep the original job ID
        imported_data['last_modified'] = datetime.now().isoformat()
        
        # Save the merged data
        if job_manager.save_job_data(job_id, imported_data):
            return jsonify({
                'success': True,
                'job_id': job_id,
                'message': 'JSON file imported successfully',
                'data_rows': len(imported_data.get('original_data', [])),
                'transformed_data_rows': len(imported_data.get('transformed_data', [])),
                'mapping_definitions': len(imported_data.get('mapping_definitions', []))
            })
        else:
            return jsonify({'success': False, 'message': 'Failed to save imported data'}), 500
            
    except Exception as e:
        current_app.logger.error(f"Error importing JSON file: {str(e)}", exc_info=True)
        return jsonify({'success': False, 'message': f'Import failed: {str(e)}'}), 500

@import_wizard_bp.route('/api/import/export-table-excel', methods=['POST'])
@requires_auth
def export_table_excel():
    """Export current table data to Excel format."""
    try:
        request_data = request.get_json()
        if not request_data or 'data' not in request_data:
            return jsonify({'success': False, 'message': 'No data to export'}), 400
        
        table_data = request_data['data']
        job_id = request_data.get('job_id')
        
        if not table_data:
            return jsonify({'success': False, 'message': 'No table data to export'}), 400
        
        # Apply NOT NEEDED logic to the data before export
        processed_data = apply_not_needed_logic_to_data(table_data)
        
        # Convert the processed data to a pandas DataFrame
        df = pd.DataFrame(processed_data)
        
        # Create Excel file in memory with styling for NOT NEEDED cells
        excel_buffer = io.BytesIO()
        with pd.ExcelWriter(excel_buffer, engine='openpyxl') as writer:
            df.to_excel(writer, sheet_name='Table Data', index=False)
            
            # Get the workbook and worksheet to apply formatting
            workbook = writer.book
            worksheet = writer.sheets['Table Data']
            
            # Define colors for different cell types
            from openpyxl.styles import PatternFill, Font, Alignment
            not_needed_fill = PatternFill(start_color="D3D3D3", end_color="D3D3D3", fill_type="solid")  # Light gray
            not_needed_font = Font(italic=True, color="666666")
            
            # Style "- NOT NEEDED" cells
            for row_idx in range(2, len(df) + 2):  # Start from row 2 (after header)
                for col_idx, col_name in enumerate(df.columns, 1):
                    cell = worksheet.cell(row=row_idx, column=col_idx)
                    cell_value = str(cell.value) if cell.value is not None else ""
                    
                    # Style "- NOT NEEDED" cells
                    if cell_value == "- NOT NEEDED":
                        cell.fill = not_needed_fill
                        cell.font = not_needed_font
                        cell.alignment = Alignment(horizontal="center")
            
            # Auto-adjust column widths
            for column in worksheet.columns:
                max_length = 0
                column_letter = column[0].column_letter
                for cell in column:
                    try:
                        if len(str(cell.value)) > max_length:
                            max_length = len(str(cell.value))
                    except:
                        pass
                adjusted_width = min(max_length + 2, 50)  # Cap at 50 characters
                worksheet.column_dimensions[column_letter].width = adjusted_width
        
        excel_buffer.seek(0)
        
        # Create filename
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        if job_id:
            filename = f"table-data-{job_id[:8]}_{timestamp}.xlsx"
        else:
            filename = f"table-data_{timestamp}.xlsx"
        
        # Create response with Excel file
        response = make_response(excel_buffer.getvalue())
        response.headers['Content-Disposition'] = f'attachment; filename="{filename}"'
        response.headers['Content-Type'] = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        return response
        
    except Exception as e:
        current_app.logger.error(f"Error exporting table data to Excel: {str(e)}", exc_info=True)
        return jsonify({'success': False, 'message': f'Export failed: {str(e)}'}), 500

@import_wizard_bp.route('/api/import/export-enhanced-excel', methods=['POST'])
@requires_auth
def export_enhanced_excel():
    """Export data with enhanced supplier-specific formatting and options."""
    try:
        request_data = request.get_json()
        if not request_data or 'data' not in request_data:
            return jsonify({'success': False, 'message': 'No data to export'}), 400
        
        table_data = request_data['data']
        export_options = request_data.get('export_options', {})
        job_id = request_data.get('job_id')
        
        if not table_data:
            return jsonify({'success': False, 'message': 'No table data to export'}), 400
        
        # Get job data if job_id is provided
        job_data = {}
        if job_id:
            job_manager = current_app.job_manager
            job_data = job_manager.load_job_data(job_id) or {}
        
        # Add job data to export options
        export_options['job_data'] = job_data
        
        # Load supplier configuration with explanations
        supplier_config = None
        try:
            from modules.supplier_config_google_sheets import supplier_config_sheets
            supplier_config = supplier_config_sheets.load_supplier_config_with_explanations()
        except Exception as e:
            current_app.logger.warning(f"Could not load supplier configuration: {str(e)}")
        
        # Create enhanced Excel export
        from modules.utils.excel_export_import import create_supplier_excel_export
        excel_buffer = create_supplier_excel_export(table_data, export_options, supplier_config)
        
        # Create filename
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        export_type = export_options.get('export_type', 'all_columns')
        
        if job_id:
            filename = f"enhanced-export-{export_type}-{job_id[:8]}_{timestamp}.xlsx"
        else:
            filename = f"enhanced-export-{export_type}_{timestamp}.xlsx"
        
        # Create response with Excel file
        response = make_response(excel_buffer.getvalue())
        response.headers['Content-Disposition'] = f'attachment; filename="{filename}"'
        response.headers['Content-Type'] = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        return response
        
    except Exception as e:
        current_app.logger.error(f"Error exporting enhanced Excel: {str(e)}", exc_info=True)
        return jsonify({'success': False, 'message': f'Enhanced export failed: {str(e)}'}), 500

@import_wizard_bp.route('/api/import/get-supplier-config', methods=['GET'])
@requires_auth
def get_supplier_config():
    """Get supplier configuration for export options."""
    try:
        from modules.supplier_config_google_sheets import supplier_config_sheets
        
        # Load both basic and detailed supplier configuration
        basic_config = supplier_config_sheets.load_supplier_family_config_from_sheets()
        detailed_config = supplier_config_sheets.load_supplier_config_with_explanations()
        
        # Get available families
        families = list(basic_config.keys()) if basic_config else []
        
        return jsonify({
            'success': True,
            'families': families,
            'basic_config': basic_config,
            'detailed_config': detailed_config,
            'has_explanations': bool(detailed_config)
        })
        
    except Exception as e:
        current_app.logger.error(f"Error getting supplier config: {str(e)}", exc_info=True)
        return jsonify({'success': False, 'message': f'Failed to load supplier configuration: {str(e)}'}), 500

@import_wizard_bp.route('/api/import/get-default-mapping-settings', methods=['GET'])
@requires_auth
def get_default_mapping_settings():
    """Get default mapping settings from Google Sheets Main worksheet."""
    try:
        from modules.google_sheets_service import google_sheets_service
        
        if not google_sheets_service.is_authenticated():
            return jsonify({
                'success': False, 
                'message': 'Google Sheets authentication required'
            }), 401
        
        # Read the Main worksheet to get default mapping settings
        prompts_data = google_sheets_service.read_sheet_data(
            google_sheets_service.mapping_sheet_id, 
            'Main'
        )
        
        if not prompts_data or len(prompts_data) < 2:
            return jsonify({
                'success': False,
                'message': 'No data found in Main worksheet'
            }), 404
        
        # Convert to DataFrame for easier processing
        import pandas as pd
        df = pd.DataFrame(prompts_data[1:], columns=prompts_data[0])
        
        # Look for default mapping settings in the first row or specific configuration
        default_mapping_type = 'Logic'  # Default fallback
        default_mapping_content = 'This is my custom prompt'  # Default fallback
        
        # Check if there are specific default mapping columns
        if 'Default_Mapping' in df.columns:
            default_values = df['Default_Mapping'].dropna()
            if len(default_values) > 0:
                default_mapping_type = default_values.iloc[0]
        
        if 'Default_Mapping_Content' in df.columns:
            default_content_values = df['Default_Mapping_Content'].dropna()
            if len(default_content_values) > 0:
                default_mapping_content = default_content_values.iloc[0]
        
        return jsonify({
            'success': True,
            'default_mapping_type': default_mapping_type,
            'default_mapping_content': default_mapping_content
        })
        
    except Exception as e:
        current_app.logger.error(f"Error getting default mapping settings: {str(e)}", exc_info=True)
        return jsonify({
            'success': False,
            'message': f'Error getting default mapping settings: {str(e)}'
        }), 500

@import_wizard_bp.route('/api/import/get-akeneo-column-configs', methods=['GET'])
@requires_auth
def get_akeneo_column_configs():
    """Get Akeneo column configurations from Google Sheets Main worksheet."""
    try:
        from modules.google_sheets_service import google_sheets_service
        
        if not google_sheets_service.is_authenticated():
            return jsonify({
                'success': False, 
                'message': 'Google Sheets authentication required'
            }), 401
        
        # Get Akeneo column configurations
        akeneo_configs = google_sheets_service.get_akeneo_column_configurations()
        
        return jsonify({
            'success': True,
            'configs': akeneo_configs,
            'count': len(akeneo_configs)
        })
        
    except Exception as e:
        current_app.logger.error(f"Error getting Akeneo column configurations: {str(e)}", exc_info=True)
        return jsonify({
            'success': False, 
            'message': f'Error: {str(e)}'
        }), 500

@import_wizard_bp.route('/api/import/apply-not-needed-logic', methods=['POST'])
@requires_auth
def apply_not_needed_logic_route():
    """Apply 'NOT NEEDED' logic to job data based on product family requirements."""
    try:
        request_data = request.get_json()
        if not request_data:
            return jsonify({'success': False, 'message': 'No data provided'}), 400
        
        job_id = request_data.get('job_id')
        if not job_id:
            return jsonify({'success': False, 'message': 'Job ID is required'}), 400
        
        job_manager = current_app.job_manager
        job_data = job_manager.load_job_data(job_id)
        if not job_data:
            return jsonify({'success': False, 'message': f'Job {job_id} not found'}), 404
        
        # Get original data and transformed data
        original_data = job_data.get('original_data', [])
        transformed_data = job_data.get('transformed_data', [])
        
        if not original_data:
            return jsonify({'success': False, 'message': 'No original data found in job'}), 400
        
        # Initialize transformed_data if not present
        if not transformed_data or len(transformed_data) != len(original_data):
            transformed_data = [{} for _ in range(len(original_data))]
            job_data['transformed_data'] = transformed_data
        
        # Apply NOT NEEDED logic row by row
        updated_count = 0
        
        for i in range(len(original_data)):
            original_row = original_data[i]
            transformed_row = transformed_data[i] if i < len(transformed_data) else {}
            
            # Apply NOT NEEDED logic
            updated_row = apply_not_needed_logic_to_row(
                transformed_row, 
                original_row,
                column_mappings=job_data.get('mapping_definitions', [])
            )
            
            # Check if anything was updated
            if updated_row != transformed_row:
                transformed_data[i] = updated_row
                updated_count += 1
        
        # Save the updated job data
        job_data['transformed_data'] = transformed_data
        job_data['last_modified'] = datetime.now().isoformat()
        
        if not job_manager.save_job_data(job_id, job_data):
            return jsonify({'success': False, 'message': 'Failed to save job data'}), 500
        
        return jsonify({
            'success': True,
            'message': f'Applied NOT NEEDED logic to {updated_count} rows',
            'updated_rows': updated_count,
            'total_rows': len(original_data)
        })
        
    except Exception as e:
        current_app.logger.error(f"Error applying NOT NEEDED logic: {str(e)}", exc_info=True)
        return jsonify({'success': False, 'message': f'Error: {str(e)}'}), 500

@import_wizard_bp.route('/api/import/get-family-requirements', methods=['POST'])
@requires_auth
def get_family_requirements():
    """Get column requirements for specific product families."""
    try:
        request_data = request.get_json()
        if not request_data:
            return jsonify({'success': False, 'message': 'No data provided'}), 400
        
        family_codes = request_data.get('family_codes', [])
        if not isinstance(family_codes, list):
            family_codes = [family_codes] if family_codes else []
        
        from modules.utils.product_family_utils import (
            get_family_column_requirements,
            get_needed_columns_for_family,
            get_not_needed_columns_for_family
        )
        
        family_requirements = {}
        for family_code in family_codes:
            if family_code:
                family_requirements[family_code] = {
                    'all_requirements': get_family_column_requirements(family_code),
                    'needed_columns': get_needed_columns_for_family(family_code),
                    'not_needed_columns': get_not_needed_columns_for_family(family_code)
                }
        
        return jsonify({
            'success': True,
            'family_requirements': family_requirements
        })
        
    except Exception as e:
        current_app.logger.error(f"Error getting family requirements: {str(e)}", exc_info=True)
        return jsonify({'success': False, 'message': f'Error: {str(e)}'}), 500

@import_wizard_bp.route('/api/import/get-default-logic-template', methods=['GET'])
@requires_auth
def get_default_logic_template():
    """Get the default logic template from the config."""
    try:
        from config.config import get_default_logic_prompt
        
        # Get the default template
        default_template = get_default_logic_prompt()
        
        return jsonify({
            'success': True,
            'template': default_template
        })
        
    except Exception as e:
        current_app.logger.error(f"Error getting default logic template: {str(e)}", exc_info=True)
        return jsonify({
            'success': False,
            'message': f'Error getting default logic template: {str(e)}'
        }), 500

@import_wizard_bp.route('/api/import/get-enabled-models', methods=['GET'])
@requires_auth
def get_enabled_models_for_import():
    """Get enabled models for the import wizard"""
    try:
        enabled_models = model_selection_service.get_enabled_models_with_details()
        default_model = model_selection_service.get_default_model()
        
        return jsonify({
            'success': True,
            'models': enabled_models,
            'default_model': default_model
        })
    except Exception as e:
        current_app.logger.error(f"Error getting enabled models for import: {str(e)}", exc_info=True)
        return jsonify({
            'success': False,
            'message': f'Error getting models: {str(e)}'
        }), 500 

@import_wizard_bp.route('/api/import/model-settings', methods=['GET'])
@requires_auth
def get_model_settings():
    """Get all available models and the default model settings."""
    try:
        enabled_models = model_selection_service.get_enabled_models_with_details()
        default_model_id = model_selection_service.get_default_model() # This gets the ID

        # Prepare the list of models in the desired format
        models_for_dropdown = []
        for model_details in enabled_models:
            models_for_dropdown.append({
                "id": model_details.get("id", model_details.get("name")), # Fallback to name if id is not present
                "name": model_details.get("display_name", model_details.get("name"))
            })
        
        # It's good practice to ensure the default_model_id exists in the list
        # Default model for logic and auto-map might be different in the future.
        # For now, we'll use the same general default.
        return jsonify({
            'success': True,
            'models': models_for_dropdown,
            'default_model_for_logic': default_model_id,
            'default_model_for_auto_map': default_model_id 
        })
    except Exception as e:
        current_app.logger.error(f"Error getting model settings: {str(e)}", exc_info=True)
        return jsonify({
            'success': False,
            'message': f'Error getting model settings: {str(e)}'
        }), 500

@import_wizard_bp.route('/api/import/clear-row', methods=['POST'])
@requires_auth
def clear_row():
    """Clear all cells in a specific row"""
    try:
        request_data = request.get_json()
        if not request_data:
            return jsonify({'success': False, 'message': 'No data provided'}), 400
        
        job_id = request_data.get('job_id')
        row_index = request_data.get('row_index')
        
        if not job_id:
            return jsonify({'success': False, 'message': 'job_id is required'}), 400
        
        if row_index is None:
            return jsonify({'success': False, 'message': 'row_index is required'}), 400
        
        redis_job_manager = current_app.redis_job_manager
        job_metadata = redis_job_manager.get_job_metadata(job_id)
        
        if not job_metadata:
            return jsonify({'success': False, 'message': f'Job {job_id} not found'}), 404
        
        # Check if row exists
        total_rows = job_metadata.get('row_count', 0)
        if row_index >= total_rows:
            return jsonify({'success': False, 'message': f'Row {row_index} does not exist'}), 400
        
        # Clear the entire row by setting all columns to None
        from models.redis_models import RedisJobDataManager
        data_manager = RedisJobDataManager(job_id)
        
        # Get all source columns for this job
        source_columns = redis_job_manager.get_source_columns(job_id)
        
        # Clear each column in the row
        cleared_columns = []
        for column_name in source_columns:
            if data_manager.update_cell(row_index, column_name, None):
                cleared_columns.append(column_name)
        
        current_app.logger.info(f"🧹 Cleared row {row_index} in job {job_id} - {len(cleared_columns)} columns cleared")
        
        # Always return success since clearing succeeds even if row was already empty
        return jsonify({
            'success': True,
            'message': f'Row {row_index + 1} cleared successfully',
            'cleared_columns': cleared_columns
        })
            
    except Exception as e:
        current_app.logger.error(f"Error clearing row: {str(e)}", exc_info=True)
        return jsonify({
            'success': False,
            'message': f'Error clearing row: {str(e)}'
        }), 500

@import_wizard_bp.route('/api/import/clear-column', methods=['POST'])
@requires_auth
def clear_column():
    """Clear all cells in a specific column"""
    try:
        request_data = request.get_json()
        if not request_data:
            return jsonify({'success': False, 'message': 'No data provided'}), 400
        
        job_id = request_data.get('job_id')
        column_name = request_data.get('column_name')
        
        if not job_id:
            return jsonify({'success': False, 'message': 'job_id is required'}), 400
        
        if not column_name:
            return jsonify({'success': False, 'message': 'column_name is required'}), 400
        
        redis_job_manager = current_app.redis_job_manager
        job_metadata = redis_job_manager.get_job_metadata(job_id)
        
        if not job_metadata:
            return jsonify({'success': False, 'message': f'Job {job_id} not found'}), 404
        
        # Clear the specified column in all rows using Redis
        from models.redis_models import RedisJobDataManager, RedisTransformationManager
        data_manager = RedisJobDataManager(job_id)
        transform_manager = RedisTransformationManager(job_id)
        
        total_rows = job_metadata.get('row_count', 0)
        cleared_row_count = 0
        
        # Clear the column data in all rows
        for row_index in range(total_rows):
            if data_manager.update_cell(row_index, column_name, None):
                cleared_row_count += 1
        
        # Also clear transformation history for this column
        transform_manager.clear_column_transformations(column_name)
        
        current_app.logger.info(f"🧹 Cleared column '{column_name}' in job {job_id} - {cleared_row_count} cells cleared")
        
        # Always return success
        return jsonify({
            'success': True,
            'message': f"Column '{column_name}' cleared successfully",
            'cleared_row_count': cleared_row_count,
            'column_name': column_name
        })
            
    except Exception as e:
        current_app.logger.error(f"Error clearing column: {str(e)}", exc_info=True)
        return jsonify({
            'success': False,
            'message': f'Error clearing column: {str(e)}'
        }), 500