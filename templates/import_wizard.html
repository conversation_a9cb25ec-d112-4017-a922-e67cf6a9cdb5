<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Import Wizard</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Select2 for better dropdowns -->
    <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
    <!-- Font Awesome Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- AG Grid for performant data tables -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/ag-grid-community@31.0.1/styles/ag-grid.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/ag-grid-community@31.0.1/styles/ag-theme-alpine.min.css">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
    <style>
        body { padding-top: 20px; }
        .job-actions a, .job-actions button {
            margin-right: 5px;
        }
        .description-text {
            font-size: 0.85rem;
            color: #666;
            max-height: 70px; /* Limit height to make description scrollable */
            overflow-y: auto;
        }
        .preview-cell {
            background-color: #f8f9fa;
            padding: 0.5rem;
            border-radius: 4px;
            margin-top: 0.25rem;
            margin-bottom: 0.25rem;
            max-height: 50px;
            overflow-y: auto;
            font-size: 0.85rem;
        }
        .compact-header {
            padding: 0.5rem;
            background-color: #f8f9fa;
            border-bottom: 1px solid #dee2e6;
        }
        .map-type-container {
            display: flex;
            gap: 0.5rem;
            align-items: center;
        }
        .logic-input {
            width: 100%;
            margin-top: 0.5rem;
        }
        .compact-ui {
            margin-bottom: 0.5rem;
        }
        .compact-card {
            margin-bottom: 0.5rem;
        }
        .preview-title {
            font-size: 0.8rem;
            color: #6c757d;
            margin-bottom: 0.25rem;
        }
        .reference-tag {
            display: inline-block;
            background-color: #e9ecef;
            padding: 2px 6px;
            border-radius: 4px;
            font-size: 0.85rem;
            margin: 2px;
            cursor: pointer;
        }

        .reference-helper {
            font-size: 0.8rem;
            color: #6c757d;
            margin-top: 0.25rem;
        }
        .mapping-column-cell {
            max-width: 90px; /* Reduced for space efficiency */
            width: auto; /* Auto-size based on content */
            transition: all 0.3s ease-in-out;
            min-width: 70px; /* Ensure readability */
        }
        .mapping-column-cell .card-header {
            cursor: pointer;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: space-between;
            min-height: 60px; /* Reduced height for space efficiency */
            padding: 4px 6px; /* Reduced padding */
        }
        .mapping-column-cell .card-header:hover {
            background-color: #e9ecef;
        }
        .mapping-column-cell .column-status-indicator {
            margin-top: auto; /* Push status badges to bottom */
        }
        .unified-preview-grid {
            border: 1px solid #dee2e6;
            border-radius: 4px;
            overflow: hidden;
        }
        
        /* AG Grid customizations for unified preview */
        #unifiedPreviewAGGrid .ag-header {
            background-color: #f8f9fa;
            border-bottom: 1px solid #dee2e6;
        }
        
        #unifiedPreviewAGGrid .ag-header-cell {
            font-weight: bold;
            font-size: 0.85rem;
        }
        
        #unifiedPreviewAGGrid .ag-row {
            border-bottom: 1px solid #eee;
        }
        
        #unifiedPreviewAGGrid .ag-row:last-child {
            border-bottom: none;
        }
        
        #unifiedPreviewAGGrid .ag-cell {
            font-size: 0.85rem;
            padding: 8px;
        }
        
        /* Align AG Grid with mapping headers */
        #unifiedPreviewAGGrid {
            margin-left: 0;
        }
        
        /* Remove any default padding that might cause misalignment */
        #unifiedPreviewAGGrid .ag-root-wrapper {
            border: none;
        }
        
        /* Ensure dropdowns appear above the data grid */
        .btn-group .dropdown-menu {
            z-index: 9999 !important;
        }
        
        /* Fix for dropdown positioning */
        .dropdown-menu.show {
            display: block !important;
        }
        
        /* Additional fixes for dropdowns */
        .apply-logic-button-group {
            position: relative;
        }
        
        /* Ensure parent containers don't clip the dropdown */
        #horizontal-mapping-container {
            overflow: visible !important;
        }
        
        /* Ensure the preview grid doesn't overlap dropdowns */
        #unifiedPreviewGrid {
            position: relative;
            z-index: 1;
        }
        
        /* Styles for the new Logic Preview Modal */
        #logicPreviewModal .tab-content {
            padding: 15px 0;
        }
        
        #logicPreviewModal .nav-tabs .nav-link {
            font-weight: 500;
            color: #495057;
        }
        
        #logicPreviewModal .nav-tabs .nav-link.active {
            font-weight: 600;
            color: #0d6efd;
        }
        
        #logicPreviewModal pre {
            background-color: #f8f9fa;
            border-radius: 4px;
            font-family: monospace;
            font-size: 0.85rem;
        }
        
        #logicPreviewModal .badge {
            font-size: 0.75rem;
            padding: 4px 6px;
            margin-right: 3px;
        }
        
        /* Style for custom row count input */
        .form-check-inline .form-control.form-control-sm {
            vertical-align: middle;
            margin-top: -2px;
        }
        
        /* Frozen mapping headers styling */
        #mappingHeadersWrapper {
            border-top: 1px solid #dee2e6;
            border-bottom: 1px solid #dee2e6;
            position: relative;
            overflow: hidden;
        }
        
        #mappingHeadersFixed {
            background: #f8f9fa !important;
            border-right: 2px solid #dee2e6 !important;
            box-shadow: 2px 0 4px rgba(0,0,0,0.1);
            position: sticky !important;
            left: 0 !important;
            z-index: 20 !important;
        }
        
        #mappingHeadersScrollable {
            scrollbar-width: thin;
            scrollbar-color: #6c757d #f1f1f1;
        }
        
        #mappingHeadersScrollable::-webkit-scrollbar {
            height: 8px;
        }
        
        #mappingHeadersScrollable::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 4px;
        }
        
        #mappingHeadersScrollable::-webkit-scrollbar-thumb {
            background: #6c757d;
            border-radius: 4px;
        }
        
        #mappingHeadersScrollable::-webkit-scrollbar-thumb:hover {
            background: #495057;
        }
        
        /* Ensure smooth scrolling sync */
        #horizontal-mapping-container {
            overflow: visible !important;
        }
        
        /* Ensure the preview grid doesn't overlap dropdowns */
        #unifiedPreviewGrid {
            position: relative;
            z-index: 1;
        }
        
        /* Ensure AG Grid and headers are properly aligned */
        #unifiedPreviewAGGrid .ag-pinned-left-cols-container {
            z-index: 15 !important;
            background: #f8f9fa !important;
            border-right: 2px solid #dee2e6 !important;
            box-shadow: 2px 0 4px rgba(0,0,0,0.1);
        }
        
        #unifiedPreviewAGGrid .ag-header .ag-pinned-left-header {
            background: #f8f9fa !important;
            border-right: 2px solid #dee2e6 !important;
        }
        
        /* Prevent layout shifts during scrolling */
        .mapping-column-cell {
            flex-shrink: 0;
            min-width: 80px;
        }

        /* PERFORMANCE FIX: Ensure edit buttons are visible */
        .mapping-column-cell .card-body {
            display: block !important;
            padding: 0.5rem !important;
        }
        
        .mapping-column-cell .map-field-btn {
            display: inline-block !important;
            visibility: visible !important;
            opacity: 1 !important;
            margin-bottom: 0.25rem;
        }
        
        .mapping-column-cell .clear-column-btn {
            display: inline-block !important;
            visibility: visible !important;
            opacity: 1 !important;
            font-size: 0.75rem;
            padding: 0.1rem 0.25rem;
            background: none;
            border: 1px solid #dc3545;
            color: #dc3545;
            border-radius: 0.25rem;
        }
        
        .mapping-column-cell .clear-column-btn:hover {
            background: #dc3545;
            color: white;
        }

        /* Modal scrolling fixes and improvements */
        .modal-dialog {
            max-height: 90vh;
            margin: 1.75rem auto;
        }

        .modal-content {
            max-height: 90vh;
            display: flex;
            flex-direction: column;
        }

        .modal-body {
            overflow-y: auto;
            flex: 1;
            max-height: calc(90vh - 120px); /* Account for header and footer */
        }

        /* Collapsible sections for mapping config modal */
        .config-section {
            border: 1px solid #dee2e6;
            border-radius: 0.375rem;
            margin-bottom: 1rem;
        }

        .config-section-header {
            background-color: #f8f9fa;
            border-bottom: 1px solid #dee2e6;
            padding: 0.75rem 1rem;
            cursor: pointer;
            user-select: none;
            display: flex;
            justify-content: space-between;
            align-items: center;
            transition: background-color 0.15s ease-in-out;
        }

        .config-section-header:hover {
            background-color: #e9ecef;
        }

        .config-section-header h6 {
            margin: 0;
            font-weight: 600;
        }

        .config-section-toggle {
            transition: transform 0.15s ease-in-out;
        }

        .config-section.collapsed .config-section-toggle {
            transform: rotate(-90deg);
        }

        .config-section-content {
            padding: 1rem;
            display: block;
        }

        .config-section.collapsed .config-section-content {
            display: none;
        }

        /* Placeholder explanations styling */
        .placeholder-explanations {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 0.375rem;
            padding: 0.75rem;
            font-size: 0.875rem;
        }

        .placeholder-explanations .badge {
            margin-right: 0.25rem;
        }

        /* Compact form styling for modal */
        .modal .form-label {
            font-weight: 600;
            margin-bottom: 0.25rem;
        }

        .modal .form-text {
            font-size: 0.75rem;
        }

        /* Preview section styling */
        .preview-section {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 0.375rem;
            max-height: 200px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="container-fluid" style="padding-left: 0; padding-right: 0;">
        <!-- Compact header with minimal controls -->
        <div class="d-flex justify-content-between align-items-center compact-header">
            <div class="d-flex align-items-center">
                <a href="/" class="btn btn-sm btn-outline-secondary">
                    <i class="fas fa-home"></i> Home
                </a>
                <button type="button" class="btn btn-sm btn-primary" onclick="loadOrCreateJob()">Load/Create Job</button>
                <button type="button" class="btn btn-sm btn-secondary ms-2" onclick="renameCurrentJob()">Rename Job</button>
                <a href="{{ url_for('import_jobs_bp.import_jobs_page_route') }}" class="btn btn-sm btn-outline-primary ms-2">
                    View All Jobs
                </a>
                <div id="currentJobDisplay" class="ms-3" style="display: none;">
                    <strong id="currentJobNameDisplay"></strong>
                    <i class="fas fa-pencil-alt ms-1" id="editJobNameIcon" style="cursor: pointer;" title="Rename Job"></i>
                </div>
            </div>
            <div>
                <button type="button" class="btn btn-sm btn-primary" id="aiMappingButton">
                    <span class="spinner-border spinner-border-sm d-none" role="status" aria-hidden="true" id="aiMappingSpinner"></span>
                    <i class="fas fa-robot"></i> Auto-Map
                </button>
                <button type="button" class="btn btn-sm btn-outline-warning" id="notesButton" data-bs-toggle="modal" data-bs-target="#notesModal">
                    <i class="fas fa-sticky-note"></i> Notes
                </button>
                <button type="button" class="btn btn-sm btn-outline-primary" id="settingsButton" data-bs-toggle="modal" data-bs-target="#settingsModal">
                    <i class="fas fa-cog"></i> Settings
                </button>
                <button type="button" class="btn btn-sm btn-outline-secondary" id="toggleOptionalColumnsButton">
                    <i class="fas fa-eye"></i> <span id="toggleOptionalColumnsText">Hide Optional</span>
                </button>
                <button type="button" class="btn btn-sm btn-info" id="saveProgressButton" style="display: none;">
                    <i class="fas fa-save"></i> Save Progress
                </button>
                <!-- PERFORMANCE: Memory usage indicator -->
                <div id="memoryIndicator" class="btn btn-sm btn-outline-secondary" style="cursor: default; display: none;">
                    <i class="fas fa-memory"></i> <span id="memoryUsage">--MB</span>
                </div>
                <div class="btn-group" role="group">
                    <button type="button" class="btn btn-sm btn-outline-success" id="exportTableExcelBtn" title="Export current table data to Excel">
                        <i class="fas fa-file-excel"></i> Export Excel
                    </button>
                    <button type="button" class="btn btn-sm btn-outline-info" id="exportTableJsonBtn" title="Export current table data to JSON">
                        <i class="fas fa-file-code"></i> Export JSON
                    </button>
                </div>
                <button type="button" class="btn btn-sm btn-success" id="applyMappingButton">
                    <i class="fas fa-check"></i> Apply Mapping
                </button>
            </div>
        </div>

        <!-- Collapsible import section -->
        <div class="compact-card">
            <div class="d-flex align-items-center p-2 gap-2">
                <button type="button" class="btn btn-sm btn-light d-flex align-items-center" data-bs-toggle="collapse" href="#importSection" role="button" aria-expanded="true" aria-controls="importSection">
                    <i class="fas fa-file-import me-1"></i> Import File <i class="fas fa-chevron-down ms-2"></i>
                </button>

                <!-- Workflow Step Buttons (conditionally shown) -->
                <div id="workflowSteps" class="d-flex justify-content-start gap-2 d-none">
                    <button type="button" class="btn btn-sm btn-info" id="determineProductFamilyBtn">
                        <i class="fas fa-sitemap"></i> Determine Product Family
                    </button>
                    <button type="button" class="btn btn-sm btn-info" id="loadAdditionalColumnsBtn">
                        <i class="fas fa-columns"></i> Load Additional Columns
                    </button>
                    <button type="button" class="btn btn-sm btn-danger" id="clearAllDataBtn"><i class="fas fa-trash-alt"></i> Clear All Data</button>
                </div>
            </div>
            <div class="collapse show" id="importSection">
                <div class="card-body py-2">
                    <!-- Google Sheets Authentication Status -->
                    <div class="row mb-2">
                        <div class="col-12">
                            <div class="d-flex align-items-center">
                                <span class="me-2">Google Sheets:</span>
                                <span id="googleAuthStatus" class="badge bg-secondary">Checking...</span>
                                <button type="button" class="btn btn-sm btn-outline-primary ms-2" id="googleAuthBtn">
                                    <i class="fab fa-google"></i> Authenticate
                                </button>
                                <button type="button" class="btn btn-sm btn-outline-secondary ms-1 d-none" id="googleLogoutBtn">
                                    <i class="fas fa-sign-out-alt"></i> Logout
                                </button>
                            </div>
                        </div>
                    </div>
                    
                    <!-- File Upload Section -->
                    
                    <form id="fileUploadForm" enctype="multipart/form-data">
                        <div class="row">
                            <div class="col-md-12 mb-2">
                                <label for="fileInput" class="form-label">Select CSV or Excel File</label>
                                <input type="file" class="form-control form-control-sm" id="fileInput" accept=".csv,.xlsx,.xls">
                            </div>
                        </div>
                        
                        <!-- Worksheet Selection Section (initially hidden) -->
                        <div class="row d-none" id="worksheetSelectionSection">
                            <div class="col-md-12 mb-2">
                                <label for="worksheetSelect" class="form-label">
                                    <i class="fas fa-table me-1"></i> Select Worksheet
                                </label>
                                <select class="form-select form-select-sm" id="worksheetSelect">
                                    <option value="">Choose a worksheet...</option>
                                </select>
                                <small class="text-muted">Select which worksheet to import from your Excel file</small>
                            </div>
                        </div>
                        
                        <div class="row mt-2">
                            <div class="col-12 d-flex gap-2">
                                <button type="button" class="btn btn-sm btn-info d-none" id="detectWorksheetsButton">
                                    <span class="spinner-border spinner-border-sm d-none" role="status" aria-hidden="true" id="detectWorksheetsSpinner"></span>
                                    <i class="fas fa-search me-1"></i> Detect Worksheets
                                </button>
                                <button type="submit" class="btn btn-sm btn-primary" id="uploadButton">
                                    <span class="spinner-border spinner-border-sm d-none" role="status" aria-hidden="true" id="uploadSpinner"></span>
                                    Upload File
                                </button>
                                <div class="btn-group" role="group">
                                    <button type="button" class="btn btn-sm btn-outline-success" id="exportExcelBtn">
                                        <i class="fas fa-file-excel"></i> Export Excel
                                    </button>
                                    <button type="button" class="btn btn-sm btn-outline-info" id="importExcelBtn">
                                        <i class="fas fa-file-upload"></i> Import JSON
                                    </button>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Hidden file input for JSON import -->
                        <input type="file" id="importExcelInput" accept=".json" style="display: none;">
                        
                    </form>
                    

                </div>
            </div>
        </div>

        <!-- Main column mapping section -->
        <div class="compact-ui d-none" id="mappingSection">
            <!-- Mapping Headers Container with Frozen Left Section -->
            <div id="mappingHeadersWrapper" style="position: relative; display: flex; margin-bottom: 0;">
                <!-- Fixed/Frozen Left Section (aligns with AG Grid row number column) -->
                <div id="mappingHeadersFixed" style="
                    width: 80px; 
                    position: sticky; 
                    left: 0; 
                    z-index: 15; 
                    background: #f8f9fa; 
                    border-right: 2px solid #dee2e6;
                    box-shadow: 2px 0 4px rgba(0,0,0,0.1);
                    flex-shrink: 0;
                ">
                    <div style="height: 200px; padding: 8px 0 0 0; display: flex; flex-direction: column; align-items: center; justify-content: center; gap: 4px;">
                        <div style="color: #6c757d; font-weight: bold; font-size: 0.75rem;"># Row</div>
                        <div style="color: #6c757d; font-size: 0.7rem;">Actions</div>
                    </div>
                </div>
                
                <!-- Scrollable Right Section (mapping columns) -->
                <div id="mappingHeadersScrollable" style="
                    display: flex; 
                    overflow-x: auto; 
                    flex: 1;
                    scrollbar-width: thin;
                    scrollbar-color: #6c757d #f1f1f1;
                ">
                    <div id="horizontal-mapping-container" style="
                        display: flex; 
                        flex-wrap: nowrap; 
                        padding: 8px 0 0 0; 
                        scroll-behavior: smooth;
                        min-width: max-content;
                    ">
                        <!-- Mapping columns will be populated dynamically by JavaScript -->
                    </div>
                </div>
            </div>
            
            <!-- Bulk Processing Progress Bar -->
            <div id="bulkProcessingProgress" class="d-none" style="position: fixed; top: 50%; left: 50%; transform: translate(-50%, -50%); z-index: 9999; width: 550px; max-width: 90vw;">
                <div class="card border-primary shadow-lg">
                    <div class="card-header bg-primary text-white">
                        <div class="d-flex align-items-center">
                            <i class="fas fa-cog fa-spin me-2"></i>
                            <span id="progressTitle">Processing AI Job</span>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <div class="d-flex justify-content-between align-items-center mb-1">
                                <span class="text-muted small">Processed: <span id="progressCurrent">0</span> / <span id="progressTotal">0</span> cells</span> 
                                <span class="text-muted small" id="progressElapsed">Elapsed: 0s</span>
                            </div>
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <span class="text-muted small" id="progressETA">ETA: calculating...</span>
                                <span class="text-muted small" id="progressPercent">0%</span>
                            </div>
                            <div class="progress mb-2" style="height: 20px;">
                                <div id="progressBar" class="progress-bar progress-bar-striped progress-bar-animated bg-primary" role="progressbar" style="width: 0%;" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100">
                                    <!-- Progress percentage can be shown inside if desired, or keep it outside -->
                                </div>
                            </div>
                            <div class="text-center">
                                <small class="text-muted" id="progressStatus">Starting...</small>
                            </div>

                            <!-- German Informational Text -->
                            <div class="mt-2 mb-2 p-2 alert alert-info d-none" id="progressGermanInfo" role="alert" style="font-size: 0.85rem;">
                                <i class="fas fa-info-circle me-1"></i>
                                Dieser Prozess kann einige Minuten dauern. Er läuft im Hintergrund. Bitte arbeite in der Zwischenzeit an etwas anderem. Das Programm gibt sein Bestes! 😉
                            </div>
                        </div>
                        
                        <!-- Error Summary (only shown if there are errors) -->
                        <div id="progressErrorSummary" class="d-none">
                            <div class="alert alert-warning mb-2">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                <span id="progressErrorCount">0</span> row(s) had errors during processing.
                            </div>
                        </div>

                        <!-- Progress Details (collapsible) -->
                        <div class="text-center mt-3">
                            <button class="btn btn-sm btn-outline-secondary me-2" type="button" data-bs-toggle="collapse" 
                                    data-bs-target="#progressDetails" aria-expanded="false">
                                <i class="fas fa-list me-1"></i> Show Details
                            </button>
                            <button type="button" class="btn btn-sm btn-outline-danger" id="progressCancelBtnBody">
                                <i class="fas fa-times me-1"></i> Cancel Job
                            </button>
                        </div>
                        
                        <div class="collapse mt-3" id="progressDetails">
                            <div class="border rounded p-2" style="max-height: 200px; overflow-y: auto;">
                                <div id="progressLog" class="small text-muted">
                                    <!-- Progress log entries will be added here -->
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="card-footer text-center d-none" id="progressFooter">
                        <button type="button" class="btn btn-primary" id="progressCloseBtn">
                            <i class="fas fa-check me-1"></i> Close
                        </button>
                    </div>
                </div>
            </div>
            
            <!-- Progress Overlay -->
            <div id="progressOverlay" class="d-none" style="position: fixed; top: 0; left: 0; width: 100%; height: 100%; background-color: rgba(0, 0, 0, 0.5); z-index: 9998;"></div>
            
            <!-- New Unified Preview Grid -->
            <div id="unifiedPreviewGrid" class="unified-preview-grid d-none" style="height: calc(100vh - 200px);">
                <!-- AG Grid Container for the unified preview -->
                <div id="unifiedPreviewAGGrid" class="ag-theme-alpine" style="height: 100%; width: 100%;"></div>
            </div>
        </div>

        <!-- Mapped Data Section -->
        <div class="d-none" id="mappedDataSection">
            <div class="d-flex justify-content-between align-items-center mb-2">
                <div>
                    <strong>Mapped Data Preview</strong>
                </div>
                <div>
                    <button type="button" class="btn btn-sm btn-success" id="exportButton">
                        <i class="fas fa-file-export"></i> Export
                    </button>
                </div>
            </div>
            <div id="mappedDataGrid" class="ag-theme-alpine" style="height: 400px; width: 100%;"></div>
        </div>
    </div>

    <!-- Notes Modal -->
    <div class="modal fade" id="notesModal" tabindex="-1" aria-labelledby="notesModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="notesModalLabel">
                        <i class="fas fa-sticky-note me-2"></i> Notes
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <textarea id="userNotes" class="form-control" rows="10" placeholder="Enter your notes here. These will persist during your session and can be referenced in logic transformations using @notes"></textarea>
                    </div>
                    <div class="text-muted small">
                        <i class="fas fa-info-circle me-1"></i>
                        Notes are automatically saved and can be referenced in logic transformations using <code>@notes</code>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                    <button type="button" class="btn btn-primary" id="saveNotesBtn">
                        <i class="fas fa-save me-1"></i> Save Notes
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Prompt Preview Modal -->
    <div class="modal fade" id="promptPreviewModal" tabindex="-1" aria-labelledby="promptPreviewModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="promptPreviewModalLabel">
                        <i class="fas fa-eye me-2"></i> Prompt Preview (First Row)
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <h6>System Prompt:</h6>
                    <pre id="previewSystemPromptContent" class="p-3 border rounded bg-light" style="white-space: pre-wrap; max-height: 200px; overflow-y: auto;"></pre>
                    
                    <h6 class="mt-3">User Prompt:</h6>
                    <pre id="previewUserPromptContent" class="p-3 border rounded bg-light" style="white-space: pre-wrap; max-height: 300px; overflow-y: auto;"></pre>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Row Source Data Detail Modal -->
    <div class="modal fade" id="rowSourceDataModal" tabindex="-1" aria-labelledby="rowSourceDataModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="rowSourceDataModalLabel">
                        <i class="fas fa-table me-2"></i> Source Data for Row <span id="modalRowNumber">1</span>
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        This shows all the original source data fields for this specific row before any mapping transformations.
                    </div>
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th style="width: 30%;">Source Column</th>
                                    <th style="width: 70%;">Value</th>
                                </tr>
                            </thead>
                            <tbody id="rowSourceDataTableBody">
                                <!-- Source data rows will be populated dynamically -->
                            </tbody>
                        </table>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Mapping Configuration Modal -->
    <div class="modal fade" id="mappingConfigModal" tabindex="-1" aria-labelledby="mappingConfigModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="mappingConfigModalLabel">
                        <i class="fas fa-cog me-2"></i> Configure Mapping
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <!-- Target Column Information -->
                    <div class="mb-3 p-3 bg-light rounded">
                        <h6 class="mb-2">Target Column: <span id="modalTargetColumnName" class="fw-bold text-primary"></span></h6>
                        <p id="modalTargetColumnDescription" class="mb-1 text-muted small"></p>
                        <div id="modalTargetColumnBadges">
                            <!-- Pflicht badge or other indicators will be shown here -->
                        </div>
                    </div>

                    <!-- Mapping Type Selection -->
                    <div class="mb-3">
                        <label class="form-label fw-bold">Mapping Type</label>
                        <select class="form-select" id="modalMappingType">
                            <option value="one-to-one">One-to-One (Direct field mapping)</option>
                            <option value="logic">Logic (AI/Custom transformation)</option>
                            <option value="string">String (Static value)</option>
                            <option value="deactivated">Deactivated (Skip this field)</option>
                        </select>
                    </div>

                    <!-- One-to-One Mapping Configuration -->
                    <div id="modalOneToOneConfig" class="mapping-config-section">
                        <div class="mb-3">
                            <label for="modalSourceColumn" class="form-label">Source Column</label>
                            <select class="form-select" id="modalSourceColumn">
                                <option value="">-- Select source column --</option>
                            </select>
                            <small class="form-text text-muted">Select the source column to map directly to this target field.</small>
                        </div>
                        <!-- Source Column Preview -->
                        <div id="modalSourcePreview" class="d-none">
                            <label class="form-label">Source Data Preview</label>
                            <div class="p-2 border rounded bg-light">
                                <div id="modalSourcePreviewContent" class="small">
                                    <!-- Sample values from source column will be shown here -->
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Logic Mapping Configuration -->
                    <div id="modalLogicConfig" class="mapping-config-section d-none">
                        <!-- Collapsible Section: Prompt Template -->
                        <div class="config-section">
                            <div class="config-section-header" data-bs-toggle="collapse" data-bs-target="#promptTemplateSection" aria-expanded="true">
                                <h6>User Prompt Template</h6>
                                <i class="fas fa-chevron-down config-section-toggle"></i>
                            </div>
                            <div id="promptTemplateSection" class="config-section-content collapse show">
                                <div class="mb-3">
                                    <div class="mb-2">
                                        <div class="input-group input-group-sm">
                                            <label class="input-group-text" for="modalColumnNameSelect">Insert column:</label>
                                            <select class="form-select form-select-sm" id="modalColumnNameSelect">
                                                <option value="">-- Select column --</option>
                                            </select>
                                            <button class="btn btn-outline-secondary" type="button" id="modalInsertColumnBtn">Insert</button>
                                        </div>
                                    </div>
                                    <textarea id="modalUserPromptTemplate" class="form-control" rows="6" style="font-family: monospace;" placeholder="Enter your prompt template here..."></textarea>
                                </div>
                            </div>
                        </div>

                        <!-- Collapsible Section: Placeholder Explanations -->
                        <div class="config-section collapsed">
                            <div class="config-section-header" data-bs-toggle="collapse" data-bs-target="#placeholderSection" aria-expanded="false">
                                <h6>Placeholder Reference</h6>
                                <i class="fas fa-chevron-down config-section-toggle"></i>
                            </div>
                            <div id="placeholderSection" class="config-section-content collapse">
                                <div class="placeholder-explanations">
                                    <div class="mb-2">
                                        <strong>Available Placeholders:</strong>
                                    </div>
                                    <div class="mb-1">
                                        <span class="badge bg-secondary">@row</span> - Current row data in JSON format
                                    </div>
                                    <div class="mb-1">
                                        <span class="badge bg-secondary">@prompt</span> - Column's prompt/description text
                                    </div>
                                    <div class="mb-1">
                                        <span class="badge bg-secondary">@notes</span> - User notes
                                    </div>
                                    <div class="mb-1">
                                        <span class="badge bg-info">@prompt_additional_data</span> - Markdown table from validation worksheet
                                    </div>
                                    <div class="mb-1">
                                        <span class="badge bg-info">@prompt_additional_data_columnname</span> or <span class="badge bg-info">@prompt_additional_data_column</span> - Name of the validation column
                                    </div>
                                    <div class="mb-1">
                                        <span class="badge bg-warning">@ColumnName</span> - Specific column values from current row
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Collapsible Section: Model and Row Selection -->
                        <div class="config-section">
                            <div class="config-section-header" data-bs-toggle="collapse" data-bs-target="#modelConfigSection" aria-expanded="true">
                                <h6>Model Configuration</h6>
                                <i class="fas fa-chevron-down config-section-toggle"></i>
                            </div>
                            <div id="modelConfigSection" class="config-section-content collapse show">
                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        <label class="form-label">LLM Model</label>
                                        <select id="modalModelSelect" class="form-select">
                                            <!-- Options will be populated dynamically -->
                                        </select>
                                    </div>
                                    <div class="col-md-6">
                                        <label class="form-label">Run on</label>
                                        <div class="mt-2">
                                            <div class="form-check form-check-inline">
                                                <input class="form-check-input" type="radio" name="modalRowSelectionOption" id="modalRunOnSingle" value="1" checked>
                                                <label class="form-check-label" for="modalRunOnSingle">1 Row</label>
                                            </div>
                                            <div class="form-check form-check-inline">
                                                <input class="form-check-input" type="radio" name="modalRowSelectionOption" id="modalRunOnThree" value="3">
                                                <label class="form-check-label" for="modalRunOnThree">3 Rows</label>
                                            </div>
                                            <div class="form-check form-check-inline">
                                                <input class="form-check-input" type="radio" name="modalRowSelectionOption" id="modalRunOnTen" value="10">
                                                <label class="form-check-label" for="modalRunOnTen">10 Rows</label>
                                            </div>
                                            <div class="form-check form-check-inline">
                                                <input class="form-check-input" type="radio" name="modalRowSelectionOption" id="modalRunOnCustom" value="custom">
                                                <label class="form-check-label" for="modalRunOnCustom">Custom</label>
                                                <input type="number" class="form-control form-control-sm d-inline-block ms-1" id="modalCustomRowCount" min="1" max="1000" value="5" style="width: 80px;" disabled>
                                            </div>
                                            <div class="form-check form-check-inline">
                                                <input class="form-check-input" type="radio" name="modalRowSelectionOption" id="modalRunOnErrors" value="errors">
                                                <label class="form-check-label" for="modalRunOnErrors">All errors <small class="text-info">(reprocess failed rows)</small></label>
                                            </div>
                                            <div class="form-check form-check-inline">
                                                <input class="form-check-input" type="radio" name="modalRowSelectionOption" id="modalRunOnAll" value="all">
                                                <label class="form-check-label" for="modalRunOnAll">All unfilled rows <small class="text-warning">(expensive)</small></label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Collapsible Section: Preview -->
                        <div class="config-section collapsed">
                            <div class="config-section-header" data-bs-toggle="collapse" data-bs-target="#previewSection" aria-expanded="false">
                                <h6>Template Preview</h6>
                                <i class="fas fa-chevron-down config-section-toggle"></i>
                            </div>
                            <div id="previewSection" class="config-section-content collapse">
                                <div class="mb-3">
                                    <div class="d-flex justify-content-between align-items-center mb-2">
                                        <label class="form-label mb-0">Rendered Template Preview (First Row)</label>
                                        <button type="button" class="btn btn-sm btn-outline-primary" id="modalGeneratePreviewBtn">
                                            <span class="spinner-border spinner-border-sm d-none" role="status" aria-hidden="true" id="modalGeneratePreviewSpinner"></span>
                                            <i class="fas fa-sync-alt me-1"></i>Generate Preview
                                        </button>
                                    </div>
                                    <pre id="modalRenderedUserPrompt" class="preview-section p-3" style="white-space: pre-wrap;">
                                        <span class="text-muted">Click "Generate Preview" to see how your template will be rendered with actual data.</span>
                                    </pre>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- String Mapping Configuration -->
                    <div id="modalStringConfig" class="mapping-config-section d-none">
                        <div class="mb-3">
                            <label for="modalStringValue" class="form-label">Static String Value</label>
                            <input type="text" class="form-control" id="modalStringValue" placeholder="Enter static value...">
                            <small class="form-text text-muted">This static value will be used for all rows in this column.</small>
                        </div>
                    </div>

                    <!-- Deactivated Configuration -->
                    <div id="modalDeactivatedConfig" class="mapping-config-section d-none">
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            This field will be excluded from the output. No data will be generated for this column.
                        </div>
                    </div>

                    <!-- Current Mapping Status -->
                    <div class="mt-3 p-2 border-top">
                        <small class="text-muted">
                            <strong>Current Status:</strong> <span id="modalCurrentStatus">Not configured</span>
                        </small>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-danger d-none" id="modalClearBtn">
                        <i class="fas fa-trash me-1"></i>Clear
                    </button>
                    <button type="button" class="btn btn-primary" id="modalSaveBtn">
                        <i class="fas fa-save me-1"></i>Save Mapping
                    </button>
                    <button type="button" class="btn btn-success d-none" id="modalRunLogicBtn">
                        <span class="spinner-border spinner-border-sm d-none" role="status" aria-hidden="true" id="modalRunLogicSpinner"></span>
                        <i class="fas fa-play me-1"></i>Run Logic
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- AI Mapping Preview Modal -->
    <div class="modal fade" id="aiMappingModal" tabindex="-1" aria-labelledby="aiMappingModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="aiMappingModalLabel">
                        <i class="fas fa-robot me-2"></i> AI Mapping Preview
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="form-check form-switch mb-3">
                        <input class="form-check-input" type="checkbox" id="includeSampleRowsToggle" checked>
                        <label class="form-check-label" for="includeSampleRowsToggle">
                            Include first 2 rows of data (provides more context)
                        </label>
                    </div>
                    
                    <div class="mb-3">
                        <label for="aiMappingPrompt" class="form-label">LLM Prompt for Auto-Mapping</label>
                        <textarea class="form-control" id="aiMappingPrompt" rows="12" style="font-family: monospace; font-size: 0.9rem;"></textarea>
                    </div>

                    <button type="button" class="btn btn-sm btn-outline-secondary mb-3" id="toggleFullPromptBtn">
                        Show/Edit Full Prompt Details
                    </button>

                    <div id="fullPromptEditContainer" class="d-none mb-3">
                        <div class="mb-2">
                            <strong>System Prompt (Fixed):</strong>
                            <pre id="systemPromptDisplay" class="form-control" style="height: auto; max-height: 150px; overflow-y: auto; background-color: #e9ecef; font-size: 0.85rem; white-space: pre-wrap;"></pre>
                        </div>
                        <div class="mb-2">
                            <label for="aiMappingUserPartPrompt" class="form-label"><strong>User Prompt (Editable):</strong></label>
                            <textarea class="form-control" id="aiMappingUserPartPrompt" rows="10" style="font-family: monospace; font-size: 0.9rem;"></textarea>
                        </div>
                    </div>
                    
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        You can edit this prompt to provide additional instructions or context to the AI model. The prompt will be used to generate mappings between source columns and target columns.
                    </div>
                </div>
                <div class="modal-footer">
                    <div class="me-auto" id="aiModelChoiceContainer">
                        <label for="aiModelDropdown" class="form-label visually-hidden">Select LLM Model</label>
                        <select id="aiModelDropdown" class="form-select form-select-sm" aria-label="Select LLM Model">
                            <!-- Options will be populated dynamically by JavaScript -->
                        </select>
                    </div>
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-primary" id="executeAiMappingBtn">
                        <span class="spinner-border spinner-border-sm d-none" role="status" aria-hidden="true" id="executeAiMappingSpinner"></span>
                        Execute AI Mapping
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Enhanced AI Mapping Modal -->
    <div class="modal fade" id="enhancedAiMappingModal" tabindex="-1" aria-labelledby="enhancedAiMappingModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="enhancedAiMappingModalLabel">
                        <i class="fas fa-robot me-2"></i> Auto-Map Columns
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <!-- Action Buttons -->
                    <div class="d-flex gap-2 mb-3">
                        <button type="button" class="btn btn-primary" id="findColumnRelationsBtn">
                            <span class="spinner-border spinner-border-sm d-none" role="status" aria-hidden="true" id="findColumnRelationsSpinner"></span>
                            <i class="fas fa-search me-1"></i> Find 1 to 1 Column Relations
                        </button>
                        <button type="button" class="btn btn-secondary" id="setUnmappedToLogicBtn">
                            <span class="spinner-border spinner-border-sm d-none" role="status" aria-hidden="true" id="setUnmappedToLogicSpinner"></span>
                            <i class="fas fa-cogs me-1"></i> Apply Configuration-Based Mappings
                        </button>
                        <button type="button" class="btn btn-warning" id="removeEmptyColumnMappingsBtn">
                            <span class="spinner-border spinner-border-sm d-none" role="status" aria-hidden="true" id="removeEmptyColumnMappingsSpinner"></span>
                            <i class="fas fa-trash-alt me-1"></i> Remove mappings of columns where all the cells are empty
                        </button>
                        <div class="btn-group" role="group">
                            <button type="button" class="btn btn-success dropdown-toggle" data-bs-toggle="dropdown" aria-expanded="false" id="runAllMappingsBtn">
                                <span class="spinner-border spinner-border-sm d-none" role="status" aria-hidden="true" id="runAllMappingsSpinner"></span>
                                <i class="fas fa-play me-1"></i> Run All Mappings
                            </button>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="#" data-rows="1">Run for 1 row</a></li>
                                <li><a class="dropdown-item" href="#" data-rows="3">Run for 3 rows</a></li>
                            </ul>
                        </div>
                    </div>

                    <!-- Suggested Mappings Table -->
                    <div id="suggestedMappingsContainer" class="d-none">
                        <h6 class="mb-3">
                            <i class="fas fa-list me-2"></i> Suggested Column Mappings
                        </h6>
                        
                        <div class="alert alert-info mb-3">
                            <i class="fas fa-info-circle me-2"></i>
                            Review the suggested column mappings below. Uncheck any mappings you don't want to apply, then click "Apply Selected Mappings".
                        </div>

                        <div class="table-responsive" style="max-height: 400px; overflow-y: auto;">
                            <table class="table table-striped table-hover" id="suggestedMappingsTable">
                                <thead class="table-dark sticky-top">
                                    <tr>
                                        <th width="60">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="selectAllMappings" checked>
                                                <label class="form-check-label" for="selectAllMappings"></label>
                                            </div>
                                        </th>
                                        <th width="250">Source Column (Example Data)</th>
                                        <th width="200">Suggested Target Column</th>
                                        <th>Description</th>
                                    </tr>
                                </thead>
                                <tbody id="suggestedMappingsTableBody">
                                    <!-- Dynamic content will be inserted here -->
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <!-- Default Logic Settings Info -->
                    <div id="defaultLogicInfo" class="mt-3">
                        <h6 class="mb-3">
                            <i class="fas fa-cogs me-2"></i> Default Mapping Settings
                        </h6>
                        <div class="alert alert-secondary">
                            <div class="d-flex align-items-start">
                                <i class="fas fa-info-circle me-2 mt-1"></i>
                                <div>
                                    <strong>Default Mapping Type:</strong> <span id="defaultMappingType">Loading...</span><br>
                                    <strong>Configuration Details:</strong><br>
                                    <code id="defaultMappingContent" class="d-block mt-1 p-2 bg-light">Loading...</code>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <div class="me-auto" id="enhancedAiModelChoiceContainer">
                        <label for="enhancedAiModelDropdown" class="form-label visually-hidden">Select LLM Model</label>
                        <select id="enhancedAiModelDropdown" class="form-select form-select-sm" aria-label="Select LLM Model">
                            <!-- Options will be populated dynamically by JavaScript -->
                        </select>
                    </div>
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-success d-none" id="applySelectedMappingsBtn">
                        <span class="spinner-border spinner-border-sm d-none" role="status" aria-hidden="true" id="applySelectedMappingsSpinner"></span>
                        <i class="fas fa-check me-1"></i> Apply Selected Mappings
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Template for mapping row -->
    <template id="mappingColumnTemplate">
        <div class="mapping-column-cell card" style="flex-shrink: 0;">
            <div class="card-header">
                <strong class="column-name"></strong>
                <div class="column-status-indicator">
                    <!-- Dynamically filled: e.g., <span class="badge bg-warning text-dark">Pflicht</span> or <i class="fas fa-check-circle text-success"></i> -->
                </div>
            </div>
            <div class="card-body p-2">
                <button type="button" class="btn btn-sm btn-outline-primary map-field-btn w-100">
                    <i class="fas fa-edit fa-sm"></i>
                </button>
                <button type="button" class="clear-column-btn" title="Clear all cells in this column">
                    <i class="fas fa-eraser"></i> Clear
                </button>
            </div>
            <div class="resize-handle"></div>
        </div>
    </template>

    <!-- Enhanced Excel Export Modal -->
    <div class="modal fade" id="enhancedExportModal" tabindex="-1" aria-labelledby="enhancedExportModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header bg-success text-white">
                    <h5 class="modal-title" id="enhancedExportModalLabel">
                        <i class="fas fa-file-excel me-2"></i> Enhanced Excel Export
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        Choose your export options. The Excel file will include supplier explanations and colored cells to guide data entry.
                    </div>

                    <!-- Export Type Selection -->
                    <div class="mb-4">
                        <h6 class="mb-3">
                            <i class="fas fa-columns me-2"></i> Export Type
                        </h6>
                        <div class="form-check mb-2">
                            <input class="form-check-input" type="radio" name="exportType" id="exportAllColumns" value="all_columns" checked>
                            <label class="form-check-label" for="exportAllColumns">
                                <strong>All columns (including empty cells)</strong>
                                <small class="text-muted d-block">Export all available columns regardless of content or mapping status</small>
                            </label>
                        </div>
                        <div class="form-check mb-2">
                            <input class="form-check-input" type="radio" name="exportType" id="exportMappedOnly" value="mapped_only">
                            <label class="form-check-label" for="exportMappedOnly">
                                <strong>Export only mapped columns</strong>
                                <small class="text-muted d-block">Only include columns that have been mapped in the wizard</small>
                            </label>
                        </div>
                        <div class="form-check mb-2">
                            <input class="form-check-input" type="radio" name="exportType" id="exportSupplierRelevant" value="supplier_relevant">
                            <label class="form-check-label" for="exportSupplierRelevant">
                                <strong>Export supplier-relevant columns</strong>
                                <small class="text-muted d-block">Include mapped columns and columns that suppliers need to fill, excluding columns suppliers don't need</small>
                            </label>
                        </div>
                    </div>

                    <!-- Supplier Configuration Status -->
                    <div class="mb-4">
                        <h6 class="mb-3">
                            <i class="fas fa-user-tie me-2"></i> Supplier Configuration
                        </h6>
                        <div id="supplierConfigStatus" class="d-flex align-items-center">
                            <div class="spinner-border spinner-border-sm me-2" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                            <span>Loading supplier configuration...</span>
                        </div>
                    </div>

                    <!-- Export Features -->
                    <div class="mb-4">
                        <h6 class="mb-3">
                            <i class="fas fa-magic me-2"></i> Export Features
                        </h6>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="card border-success">
                                    <div class="card-body py-2">
                                        <h6 class="card-title text-success mb-1">
                                            <i class="fas fa-comment-alt me-1"></i> Explanations
                                        </h6>
                                        <small class="text-muted">Supplier guidance text in row 2</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="card border-warning">
                                    <div class="card-body py-2">
                                        <h6 class="card-title text-warning mb-1">
                                            <i class="fas fa-palette me-1"></i> Color Coding
                                        </h6>
                                        <small class="text-muted">Empty cells highlighted for supplier input</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="fas fa-times me-1"></i> Cancel
                    </button>
                    <button type="button" class="btn btn-success" id="executeEnhancedExport">
                        <span class="spinner-border spinner-border-sm d-none me-2" role="status"></span>
                        <i class="fas fa-download me-1"></i> Export Excel
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Apply NOT NEEDED Logic Information Modal -->
    <div class="modal fade" id="applyNotNeededModal" tabindex="-1" aria-labelledby="applyNotNeededModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header bg-info text-white">
                    <h5 class="modal-title" id="applyNotNeededModalLabel">
                        <i class="fas fa-filter me-2"></i> Apply NOT NEEDED Logic
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="alert alert-info mb-4">
                        <div class="d-flex">
                            <i class="fas fa-info-circle me-3 mt-1"></i>
                            <div>
                                <strong>What does this feature do?</strong><br>
                                <small>This feature automatically marks columns as "- NOT NEEDED" when they don't apply to specific product families, helping suppliers focus on relevant fields only.</small>
                            </div>
                        </div>
                    </div>

                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="card border-success mb-3">
                                <div class="card-header bg-success text-white">
                                    <i class="fas fa-check me-2"></i> Benefits
                                </div>
                                <div class="card-body">
                                    <ul class="list-unstyled mb-0">
                                        <li class="mb-2"><i class="fas fa-check-circle text-success me-2"></i> Reduces supplier confusion</li>
                                        <li class="mb-2"><i class="fas fa-check-circle text-success me-2"></i> Faster data entry</li>
                                        <li class="mb-2"><i class="fas fa-check-circle text-success me-2"></i> Better data quality</li>
                                        <li class="mb-0"><i class="fas fa-check-circle text-success me-2"></i> Automated processing</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card border-primary mb-3">
                                <div class="card-header bg-primary text-white">
                                    <i class="fas fa-cogs me-2"></i> How it Works
                                </div>
                                <div class="card-body">
                                    <ol class="list-unstyled mb-0">
                                        <li class="mb-2"><span class="badge bg-primary me-2">1</span> Identifies product family for each row</li>
                                        <li class="mb-2"><span class="badge bg-primary me-2">2</span> Checks column requirements per family</li>
                                        <li class="mb-2"><span class="badge bg-primary me-2">3</span> Marks irrelevant columns as "- NOT NEEDED"</li>
                                        <li class="mb-0"><span class="badge bg-primary me-2">4</span> Preserves all existing data</li>
                                    </ol>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="card border-warning mb-4">
                        <div class="card-header bg-warning text-dark">
                            <i class="fas fa-lightbulb me-2"></i> Example
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <h6 class="text-muted">Before:</h6>
                                    <div class="bg-light p-2 rounded">
                                        <small>
                                            <strong>Door Lock Product:</strong><br>
                                            • ABSTAND_FALLE_RIEGEL: 45mm<br>
                                            • TUERSCHLOSS_DORNMASS_MM: <em>[empty]</em><br>
                                            • ARTIKELNAME: <em>[empty]</em><br>
                                            • EAN: <em>[empty]</em>
                                        </small>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <h6 class="text-muted">After:</h6>
                                    <div class="bg-light p-2 rounded">
                                        <small>
                                            <strong>Door Lock Product:</strong><br>
                                            • ABSTAND_FALLE_RIEGEL: 45mm<br>
                                            • TUERSCHLOSS_DORNMASS_MM: <em>[empty - needs input]</em><br>
                                            • ARTIKELNAME: <span class="text-muted">- NOT NEEDED</span><br>
                                            • EAN: <span class="text-muted">- NOT NEEDED</span>
                                        </small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="alert alert-warning">
                        <div class="d-flex">
                            <i class="fas fa-exclamation-triangle me-3 mt-1"></i>
                            <div>
                                <strong>Important:</strong><br>
                                <small>
                                    • This action only affects <strong>empty cells</strong> - existing data is never modified<br>
                                    • Product family information must be present in your data<br>
                                    • You can always manually edit cells afterward if needed
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="fas fa-times me-1"></i> Cancel
                    </button>
                    <button type="button" class="btn btn-info" id="confirmApplyNotNeeded">
                        <i class="fas fa-filter me-1"></i> Apply NOT NEEDED Logic
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Settings Modal -->
    <div class="modal fade" id="settingsModal" tabindex="-1" aria-labelledby="settingsModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="settingsModalLabel">
                        <i class="fas fa-cog me-2"></i> Settings
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <!-- Google Sheets Cache Section -->
                    <div class="card mb-3">
                        <div class="card-header">
                            <h6 class="mb-0">
                                <i class="fab fa-google me-2"></i> Google Sheets Data Cache
                            </h6>
                        </div>
                        <div class="card-body">
                            <p class="text-muted mb-3">
                                Refresh the cached Google Sheets data to get the latest mapping definitions and validation data.
                                This helps prevent API rate limit errors.
                            </p>
                            
                            <div class="alert alert-info mb-3">
                                <div class="d-flex align-items-start">
                                    <i class="fab fa-google me-2 mt-1"></i>
                                    <div>
                                        <strong>Edit Configuration Data:</strong><br>
                                        <small class="text-muted">
                                            You can edit mapping definitions and validation data directly in Google Sheets, 
                                            then click "Refresh Google Sheets Data" below to load your changes.
                                        </small><br>
                                        <a href="https://docs.google.com/spreadsheets/d/1ZrNPK9QAXbjtyhc0XvCfU1Ca9eecec0JK5sOkns6QGY/edit?gid=1723212414#gid=1723212414" 
                                           target="_blank" class="btn btn-sm btn-outline-primary mt-2">
                                            <i class="fas fa-external-link-alt me-1"></i>Open Google Sheets Configuration
                                        </a>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="d-flex align-items-center mb-3">
                                <button type="button" class="btn btn-primary" id="refreshCacheBtn">
                                    <span class="spinner-border spinner-border-sm d-none" role="status" aria-hidden="true" id="refreshCacheSpinner"></span>
                                    <i class="fas fa-sync-alt me-1"></i> Refresh Google Sheets Data
                                </button>
                                <div class="ms-3">
                                    <span class="badge bg-secondary" id="cacheStatus">Unknown</span>
                                </div>
                            </div>
                            
                            <!-- Cache Status Display -->
                            <div id="cacheStatusDetails" class="d-none">
                                <div class="row">
                                    <div class="col-md-6">
                                        <small class="text-muted">Mapping Definitions:</small>
                                        <div id="mappingDefinitionsCount" class="fw-bold">-</div>
                                    </div>
                                    <div class="col-md-6">
                                        <small class="text-muted">Available Worksheets:</small>
                                        <div id="availableWorksheetsCount" class="fw-bold">-</div>
                                    </div>
                                </div>
                                <div class="row mt-2">
                                    <div class="col-md-6">
                                        <small class="text-muted">Worksheets Loaded:</small>
                                        <div id="worksheetsLoadedCount" class="fw-bold">-</div>
                                    </div>
                                    <div class="col-md-6">
                                        <small class="text-muted">Validation Data Sets:</small>
                                        <div id="validationDataCount" class="fw-bold">-</div>
                                    </div>
                                </div>
                                <div class="row mt-2">
                                    <div class="col-12">
                                        <small class="text-muted">Last Updated:</small>
                                        <div id="cacheTimestamp" class="fw-bold">-</div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Error Details -->
                            <div id="cacheErrorDetails" class="d-none"></div>
                            
                            <!-- Error Display -->
                            <div id="cacheErrorDisplay" class="alert alert-danger d-none" role="alert">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                <span id="cacheErrorMessage"></span>
                            </div>
                            
                            <!-- Success Display -->
                            <div id="cacheSuccessDisplay" class="alert alert-success d-none" role="alert">
                                <i class="fas fa-check-circle me-2"></i>
                                <span id="cacheSuccessMessage"></span>
                            </div>
                        </div>
                    </div>
                    
                    <!-- LLM Cache Management Section -->
                    <div class="card mb-3">
                        <div class="card-header">
                            <h6 class="mb-0">
                                <i class="fas fa-brain me-2"></i> LLM Cache Management
                            </h6>
                        </div>
                        <div class="card-body">
                            <p class="text-muted mb-3">
                                Manage the cache for Large Language Model (LLM) responses. This cache helps speed up repetitive queries and reduce API costs.
                            </p>
                            <div class="mb-3">
                                <a href="/model-settings" class="btn btn-sm btn-outline-primary">
                                    <i class="fas fa-cogs me-1"></i> Go to Model Configuration
                                </a>
                            </div>

                            <h6 class="text-muted small">Cache Statistics:</h6>
                            <div id="llmCacheStatsContainer" class="mb-3">
                                <div class="row">
                                    <div class="col-md-6">
                                        <small>Total Cached Items: <span id="llmCacheTotalItems" class="fw-bold">Loading...</span></small><br>
                                        <small>Active Entries: <span id="llmCacheActiveEntries" class="fw-bold">Loading...</span></small><br>
                                        <small>Cache Size (MB): <span id="llmCacheSize" class="fw-bold">Loading...</span></small>
                                    </div>
                                    <div class="col-md-6">
                                        <small>Total Requests (Overall): <span id="llmCacheTotalRequests" class="fw-bold">Loading...</span></small><br>
                                        <small>Cache Hits (Overall): <span id="llmCacheHits" class="fw-bold">Loading...</span></small><br>
                                        <small>Cache Misses (Overall): <span id="llmCacheMisses" class="fw-bold">Loading...</span></small><br>
                                        <small>Hit Rate (Overall): <span id="llmCacheHitRate" class="fw-bold">Loading...</span></small>
                                    </div>
                                </div>
                                <div id="llmCacheError" class="alert alert-danger mt-2 d-none">Error loading cache stats.</div>
                                <div id="llmCacheLoading" class="text-center mt-2">
                                    <div class="spinner-border spinner-border-sm" role="status">
                                        <span class="visually-hidden">Loading...</span>
                                    </div>
                                    <small> Loading stats...</small>
                                </div>
                            </div>

                            <h6 class="text-muted small">Cache Actions:</h6>
                            <button type="button" class="btn btn-danger me-2" id="purgeLlmCacheBtn">
                                <span class="spinner-border spinner-border-sm d-none" role="status" aria-hidden="true" id="purgeLlmCacheSpinner"></span>
                                <i class="fas fa-trash-alt me-1"></i> Purge Entire LLM Cache
                            </button>
                             <button type="button" class="btn btn-warning" id="purgeExpiredLlmCacheBtn">
                                <span class="spinner-border spinner-border-sm d-none" role="status" aria-hidden="true" id="purgeExpiredLlmCacheSpinner"></span>
                                <i class="fas fa-broom me-1"></i> Purge Expired LLM Cache
                            </button>
                            <div id="purgeLlmCacheStatus" class="mt-2 small"></div>
                        </div>
                    </div>
                    
                    <!-- Additional Settings can be added here in the future -->
                    <div class="card">
                        <div class="card-header">
                            <h6 class="mb-0">
                                <i class="fas fa-info-circle me-2"></i> Information
                            </h6>
                        </div>
                        <div class="card-body">
                            <p class="text-muted mb-0">
                                Additional settings and configuration options will be available here in future updates.
                            </p>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Include Google Auth Modal -->
    {% include 'google_auth_modal.html' %}

    <!-- jQuery, Bootstrap, and other scripts -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/ag-grid-community@31.0.1/dist/ag-grid-community.min.js"></script>
    <script src="{{ url_for('static', filename='js/startup_auth_check.js') }}"></script>
    <script src="{{ url_for('static', filename='js/beta_modal.js') }}"></script>
    <script src="{{ url_for('static', filename='js/import_wizard_utils.js') }}"></script>
    <script src="{{ url_for('static', filename='js/bulk_processing_progress.js') }}"></script>
    <script src="{{ url_for('static', filename='js/import_wizard_core.js') }}"></script>

    <!-- Import Wizard UI Components -->
    <script src="{{ url_for('static', filename='js/import_wizard_ui_components/collapsible_handler.js') }}"></script>
    <script src="{{ url_for('static', filename='js/import_wizard_ui_components/misc_ui_helpers.js') }}"></script>
    <script src="{{ url_for('static', filename='js/import_wizard_ui_components/notes_handler.js') }}"></script>
    <script src="{{ url_for('static', filename='js/import_wizard_ui_components/unified_preview_ag_grid.js') }}"></script>
    <script src="{{ url_for('static', filename='js/import_wizard_ui_components/logic_input_autocomplete.js') }}"></script>
    <script src="{{ url_for('static', filename='js/import_wizard_ui_components/mapping_ui_generator.js') }}"></script>
    <script src="{{ url_for('static', filename='js/import_wizard_ui_components/mapping_config_modal.js') }}"></script>
    <script src="{{ url_for('static', filename='js/import_wizard_ui_components/validation_dropdown.js') }}"></script>
    <script src="{{ url_for('static', filename='js/import_wizard_ui_components/ai_mapping_handler.js') }}"></script>
    <script src="{{ url_for('static', filename='js/import_wizard_ui_components/enhanced_ai_mapping_handler.js') }}"></script>
    <script src="{{ url_for('static', filename='js/import_wizard_ui_components/mapped_data_display.js') }}"></script>

    <script src="{{ url_for('static', filename='js/import_wizard_job_manager.js') }}"></script>
    <script src="{{ url_for('static', filename='js/import_wizard_mapping_logic.js') }}"></script>
    <script src="{{ url_for('static', filename='js/excel_worksheet_helper.js') }}"></script>
    <script src="{{ url_for('static', filename='js/excel_worksheet_selector.js') }}"></script>
    <script src="{{ url_for('static', filename='js/google_sheets_integration.js') }}"></script>
    <script src="{{ url_for('static', filename='js/settings_modal.js') }}"></script>
    <script src="{{ url_for('static', filename='js/excel_import_export.js') }}"></script>
    <script src="{{ url_for('static', filename='js/table_export.js') }}"></script>
    <script src="{{ url_for('static', filename='js/enhanced_excel_export.js') }}"></script>
    <script src="{{ url_for('static', filename='js/product_family_handler.js') }}"></script>
</body>
</html> 