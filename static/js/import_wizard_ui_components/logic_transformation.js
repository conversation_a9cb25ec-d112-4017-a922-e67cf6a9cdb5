/**
 * Logic Transformation Handler for Import Wizard
 * Handles logic application, bulk processing, and error retry functionality
 */

/**
 * Apply logic transformation to grid (client-side preview)
 */
function applyLogicTransformationToGrid(targetColumn, logic) {
    if (!originalData || originalData.length === 0) {
        showAlert('error', 'No data available for transformation');
        return;
    }
    
    console.log(`Applying logic transformation to grid for column: ${targetColumn}`);
    console.log(`Logic: ${logic}`);
    
    // This is a client-side preview - actual transformation happens on server
    // For now, just show a preview message
    showAlert('info', `Preview: Logic "${logic}" would be applied to ${targetColumn}`);
}

/**
 * Apply logic with row selection and model choice
 */
function applyLogicWithRowSelection(targetColumnName, logicInputElement, columnElement, rowCount, selectedModel) {
    if (!logicInputElement || !logicInputElement.length) {
        showAlert('error', 'Logic input element not found');
        return;
    }
    
    const logic = logicInputElement.val().trim();
    if (!logic) {
        showAlert('error', 'Please enter logic to apply');
        return;
    }
    
    // Show confirmation dialog for large datasets
    if (rowCount > 100) {
        const confirmed = confirm(`This will process ${rowCount} rows. This may take some time. Continue?`);
        if (!confirmed) {
            return;
        }
    }
    
    console.log(`Applying logic with row selection - Column: ${targetColumnName}, Rows: ${rowCount}, Model: ${selectedModel}`);
    
    // Call the main transformation function
    applyLogicTransformationWithRowLimit(targetColumnName, logic, columnElement, rowCount, selectedModel);
}

/**
 * Call bulk apply logic and update client with progress
 */
function callBulkApplyLogicAndUpdateClient(targetColumn, logic, currentNotes, prompt, batchSize, selectedModel, columnElement) {
    if (!currentJobId) {
        showAlert('error', 'No active job ID. Please upload a file first.');
        return Promise.reject('No active job ID');
    }
    if (!originalData || originalData.length === 0) {
        showAlert('error', 'Original data is not available.');
        return Promise.reject('Original data not available');
    }

    const totalRows = originalData.length;
    
    // Always use progress-enabled function if available
    if (typeof callBulkApplyLogicWithProgress === 'function') {
        console.log(`Using progress-enabled bulk processing for ${totalRows} rows`);
        return callBulkApplyLogicWithProgress(targetColumn, logic, currentNotes, prompt, batchSize, selectedModel, columnElement);
    }

    // Fallback to original implementation if progress function not available
    console.log(`Fallback to standard bulk processing for ${totalRows} rows`);

    // Show loading spinner on the apply button
    let applyBtn;
    if (columnElement && columnElement.length) {
        applyBtn = columnElement.find('.apply-logic-btn');
        if (applyBtn && applyBtn.length) {
            applyBtn.prop('disabled', true).html('<i class="fas fa-cog fa-spin"></i> Processing...');
        }
    }
    
    if (!applyBtn || !applyBtn.length) {
        showAlert('info', `Processing ${totalRows} rows for ${targetColumn}...`);
        console.log(`Processing ${totalRows} rows for ${targetColumn}...`);
    }

    // Use high-performance endpoint for large datasets
    const useHighPerformance = originalData && originalData.length > 100;
    const endpoint = useHighPerformance ?
        `/api/import/jobs/${currentJobId}/high-performance-bulk-apply-logic` :
        '/api/import/bulk-apply-logic';

    const requestBody = {
        job_id: currentJobId,
        column_name: targetColumn,
        logic: logic,
        references: {
            notes: currentNotes,
            prompt: prompt,
            target_column: targetColumn
        },
        model_name: selectedModel
    };

    // Add data and batch_size only for regular endpoint
    if (!useHighPerformance) {
        requestBody.data = originalData;
        requestBody.batch_size = batchSize;
    }

    if (useHighPerformance) {
        console.log('Using high-performance processing for', originalData.length, 'rows');
    }

    return fetch(endpoint, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(requestBody)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            console.log(`Successfully processed ${targetColumn}:`, data.results);
            
            // Initialize storage if needed
            if (!window.cellLlmDetails[targetColumn]) {
                window.cellLlmDetails[targetColumn] = {};
            }
            
            // Process results
            data.results.forEach(result => {
                if (result.isError) {
                    columnResults[targetColumn][result.rowIndex] = `Error: ${result.error}`;
                    window.cellLlmDetails[targetColumn][result.rowIndex] = { 
                        renderedLogic: result.renderedLogic || 'N/A (error)', 
                        error: result.error 
                    };
                } else {
                    columnResults[targetColumn][result.rowIndex] = result.transformedValue;
                    window.cellLlmDetails[targetColumn][result.rowIndex] = { 
                        renderedLogic: result.renderedLogic, 
                        rawResponse: result.rawResponse 
                    };
                    
                    // Store prompt preview if available
                    if (result.preview && !columnPromptPreviews[targetColumn]) {
                        columnPromptPreviews[targetColumn] = {
                            system: result.preview.system_prompt,
                            user: result.preview.user_prompt
                        };
                    }
                }
            });
            
            updateUnifiedPreviewGrid();
            showAlert('success', `Applied logic to ${totalRows} rows in ${targetColumn}.`);

            // Update column status display
            if (typeof updateColumnDisplayStatus === 'function' && columnElement) {
                const targetColDef = mappingDefinitions.find(def => def.column === targetColumn);
                const isPflicht = targetColDef ? targetColDef.pflicht : false;
                updateColumnDisplayStatus(targetColumn, columnElement.find('.column-status-indicator'), isPflicht);
            }

        } else {
            console.error(`Error processing ${targetColumn}:`, data.message);
            showAlert('error', `Error for ${targetColumn}: ${data.message || 'Unknown error'}`);
        }
        
        return data;
    })
    .catch(error => {
        console.error(`Network error or other failure for ${targetColumn}:`, error);
        showAlert('error', `Failed to process ${targetColumn}: ${error.message}`);
        return Promise.reject(error);
    })
    .finally(() => {
        if (applyBtn && applyBtn.length) {
            applyBtn.prop('disabled', false).html('Apply Logic');
        }
    });
}

/**
 * Apply logic transformation with row limit
 */
function applyLogicTransformationWithRowLimit(targetColumn, logic, columnElement, rowLimit, selectedModel) {
    if (!originalData || originalData.length === 0) {
        showAlert('error', 'No original data available');
        return;
    }
    
    const currentNotes = $('#userNotes').val() || userNotes;
    const promptAttr = columnElement.attr('data-prompt') || '';
    
    console.log(`Applying logic transformation with row limit - Column: ${targetColumn}, Limit: ${rowLimit}, Model: ${selectedModel}`);
    
    // Determine batch size based on row limit
    let batchSize = 10; // Default batch size
    if (rowLimit <= 50) {
        batchSize = 5;
    } else if (rowLimit <= 100) {
        batchSize = 10;
    } else {
        batchSize = 20;
    }
    
    // Show processing state
    if (columnElement && columnElement.length) {
        const applyBtn = columnElement.find('.apply-logic-btn');
        if (applyBtn.length) {
            applyBtn.prop('disabled', true).html('<i class="fas fa-cog fa-spin"></i> Processing...');
        }
    }
    
    // Call the server to process the transformation
    applyLogicTransformationToServer(targetColumn, logic, currentNotes, promptAttr, rowLimit, selectedModel, columnElement)
        .finally(() => {
            // Reset button state
            if (columnElement && columnElement.length) {
                const applyBtn = columnElement.find('.apply-logic-btn');
                if (applyBtn.length) {
                    applyBtn.prop('disabled', false).html('Apply Logic');
                }
            }
        });
}

/**
 * Apply logic transformation to server
 */
function applyLogicTransformationToServer(targetColumn, logic, currentNotes, prompt, rowLimit, selectedModel, columnElement) {
    if (!currentJobId) {
        showAlert('error', 'No active job ID. Please upload a file first.');
        return Promise.reject('No active job ID');
    }
    
    if (!originalData || originalData.length === 0) {
        showAlert('error', 'Original data is not available.');
        return Promise.reject('Original data not available');
    }
    
    // Prepare data for processing (limited by rowLimit)
    const dataToProcess = rowLimit ? originalData.slice(0, rowLimit) : originalData;
    const actualRowCount = dataToProcess.length;
    
    console.log(`Sending ${actualRowCount} rows to server for processing`);
    
    return fetch('/api/import/apply-logic-transformation', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            job_id: currentJobId,
            column_name: targetColumn,
            logic: logic,
            data: dataToProcess,
            references: {
                notes: currentNotes,
                prompt: prompt,
                target_column: targetColumn
            },
            model_name: selectedModel
        })
    })
    .then(response => {
        if (!response.ok) {
            throw new Error(`Server responded with status: ${response.status}`);
        }
        return response.json();
    })
    .then(data => {
        if (data.success) {
            console.log(`Successfully processed ${targetColumn}:`, data.results);
            
            // Initialize storage
            if (!window.columnResults[targetColumn]) {
                window.columnResults[targetColumn] = new Array(originalData.length).fill('');
            }
            if (!window.cellLlmDetails[targetColumn]) {
                window.cellLlmDetails[targetColumn] = {};
            }
            
            // Process results
            data.results.forEach((result, index) => {
                if (result.isError) {
                    window.columnResults[targetColumn][index] = `Error: ${result.error}`;
                    window.cellLlmDetails[targetColumn][index] = { 
                        renderedLogic: result.renderedLogic || 'N/A (error)', 
                        error: result.error 
                    };
                } else {
                    window.columnResults[targetColumn][index] = result.transformedValue;
                    window.cellLlmDetails[targetColumn][index] = { 
                        renderedLogic: result.renderedLogic, 
                        rawResponse: result.rawResponse 
                    };
                }
            });
            
            // Store prompt preview if available
            if (data.results.length > 0 && data.results[0].preview) {
                if (!window.columnPromptPreviews) {
                    window.columnPromptPreviews = {};
                }
                window.columnPromptPreviews[targetColumn] = {
                    system: data.results[0].preview.system_prompt,
                    user: data.results[0].preview.user_prompt
                };
            }
            
            // Update the preview grid
            if (typeof updateUnifiedPreviewGrid === 'function') {
                updateUnifiedPreviewGrid();
            }
            
            showAlert('success', `Applied logic to ${actualRowCount} rows in ${targetColumn}`);
            
            // Update column status
            if (typeof updateColumnStatus === 'function' && columnElement) {
                const statusElement = columnElement.find('.column-status-indicator');
                const columnDef = mappingDefinitions.find(def => def.column === targetColumn);
                const isRequired = columnDef ? (columnDef.pflicht !== false && columnDef.pflicht !== 'false') : true;
                updateColumnStatus(targetColumn, statusElement, isRequired);
            }
            
        } else {
            console.error(`Error processing ${targetColumn}:`, data.message);
            showAlert('error', `Error processing ${targetColumn}: ${data.message || 'Unknown error'}`);
        }
        
        return data;
    })
    .catch(error => {
        console.error(`Error in logic transformation for ${targetColumn}:`, error);
        showAlert('error', `Failed to process ${targetColumn}: ${error.message}`);
        return Promise.reject(error);
    });
}

/**
 * Apply logic transformation to specific error rows
 */
function applyLogicToErrorRows(targetColumn, logic, columnElement, selectedModel) {
    if (!columnResults || !columnResults[targetColumn]) {
        showAlert('info', `No results found for column ${targetColumn} to check for errors.`);
        return;
    }

    const errorRowIndices = [];
    for (let i = 0; i < columnResults[targetColumn].length; i++) {
        if (typeof columnResults[targetColumn][i] === 'string' && columnResults[targetColumn][i].startsWith('Error:')) {
            errorRowIndices.push(i);
        }
    }

    if (errorRowIndices.length === 0) {
        showAlert('info', `No rows with errors found for column ${targetColumn}.`);
        return;
    }

    const currentNotes = $('#userNotes').val() || userNotes;
    const promptAttr = columnElement.attr('data-prompt') || '';

    // Show processing state
    if (columnElement && columnElement.length) {
        const applyBtn = columnElement.find('.apply-logic-btn');
        if (applyBtn.length) {
             applyBtn.prop('disabled', true).html('<i class="fas fa-cog fa-spin"></i> Re-applying...');
        } else {
            console.log(`Attempting to re-apply logic for errors in ${targetColumn}`);
        }
    }
    
    // Call the bulk apply function for specific rows
    callBulkApplyLogicForSpecificRows(targetColumn, logic, currentNotes, promptAttr, errorRowIndices, selectedModel, columnElement)
        .finally(() => {
            // Reset button state
            if (columnElement && columnElement.length) {
                const applyBtn = columnElement.find('.apply-logic-btn');
                 if (applyBtn.length) {
                    applyBtn.prop('disabled', false).html('Apply Logic');
                }
            }
        });
}

/**
 * Call bulk apply logic for specific row indices (error retry)
 */
async function callBulkApplyLogicForSpecificRows(targetColumn, logic, currentNotes, prompt, errorRowIndices, selectedModel, columnElement) {
    if (!currentJobId) {
        showAlert('error', 'No active job ID. Please upload a file first.');
        return Promise.reject('No active job ID');
    }
    if (!originalData || originalData.length === 0) {
        showAlert('error', 'Original data is not available.');
        return Promise.reject('Original data not available');
    }

    const totalErrorRows = errorRowIndices.length;
    
    // Use progress-enabled function if available
    if (typeof callBulkApplyLogicForSpecificRowsWithProgress === 'function') {
        console.log(`Using progress-enabled error row processing for ${totalErrorRows} error rows`);
        return callBulkApplyLogicForSpecificRowsWithProgress(targetColumn, logic, currentNotes, prompt, errorRowIndices, selectedModel, columnElement);
    }

    // Fallback implementation
    console.log(`Fallback to standard error row processing for ${totalErrorRows} error rows`);

    const dataForRequest = errorRowIndices.map(index => originalData[index]);
    const originalIndicesForRequest = errorRowIndices;

    // Show processing message
    let applyBtn;
    if (columnElement && columnElement.length) {
        applyBtn = columnElement.find('.apply-logic-btn');
        if (applyBtn && applyBtn.length) {
            applyBtn.prop('disabled', true).html('<i class="fas fa-cog fa-spin"></i> Processing Errors...');
        }
    }
    
    if (!applyBtn || !applyBtn.length) {
        showAlert('info', `Re-applying logic to ${errorRowIndices.length} error(s) in ${targetColumn}...`);
        console.log(`Re-applying logic to ${errorRowIndices.length} error(s) in ${targetColumn}...`);
    }

    try {
        const response = await fetch('/api/import/bulk-apply-logic', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                job_id: currentJobId,
                column_name: targetColumn,
                logic: logic,
                data: dataForRequest,
                row_indices: originalIndicesForRequest,
                error_rows_only_mode: true,
                references: {
                    notes: currentNotes,
                    prompt: prompt,
                    target_column: targetColumn
                },
                model_name: selectedModel
            })
        });

        const data = await response.json();

        if (data.success) {
            console.log(`Successfully processed errors for ${targetColumn}:`, data.results);
            
            if (!window.cellLlmDetails[targetColumn]) {
                window.cellLlmDetails[targetColumn] = {};
            }
            
            data.results.forEach(result => {
                if (result.isError) {
                    columnResults[targetColumn][result.rowIndex] = `Error: ${result.error}`;
                    window.cellLlmDetails[targetColumn][result.rowIndex] = { 
                        renderedLogic: result.renderedLogic || 'N/A (error)', 
                        error: result.error 
                    };
                } else {
                    columnResults[targetColumn][result.rowIndex] = result.transformedValue;
                    window.cellLlmDetails[targetColumn][result.rowIndex] = { 
                        renderedLogic: result.renderedLogic, 
                        rawResponse: result.rawResponse 
                    };
                    if (result.preview && !columnPromptPreviews[targetColumn]) {
                         columnPromptPreviews[targetColumn] = {
                            system: result.preview.system_prompt,
                            user: result.preview.user_prompt
                        };
                    }
                }
            });
            
            updateUnifiedPreviewGrid();
            showAlert('success', `Re-applied logic to ${errorRowIndices.length} error(s) in ${targetColumn}.`);

            // Update column status display
            if (typeof updateColumnDisplayStatus === 'function' && columnElement) {
                const targetColDef = mappingDefinitions.find(def => def.column === targetColumn);
                const isPflicht = targetColDef ? targetColDef.pflicht : false;
                updateColumnDisplayStatus(targetColumn, columnElement.find('.column-status-indicator'), isPflicht);
            }

        } else {
            console.error(`Error re-applying logic to errors for ${targetColumn}:`, data.message);
            showAlert('error', `Error for ${targetColumn} (re-apply errors): ${data.message || 'Unknown error'}`);
        }
        return data;

    } catch (error) {
        console.error(`Network error or other failure in re-applying logic to errors for ${targetColumn}:`, error);
        showAlert('error', `Failed to re-apply logic for ${targetColumn} errors: ${error.message}`);
        return Promise.reject(error);
    } finally {
        if (applyBtn && applyBtn.length) {
            applyBtn.prop('disabled', false).html('Apply Logic');
        }
    }
}

/**
 * Simple wrapper for backward compatibility
 */
function applyLogicTransformation(targetColumn, logic, columnElement) {
    // Default to processing all rows with gpt-4o-mini model
    const rowLimit = originalData ? originalData.length : 100;
    const selectedModel = 'gpt-4o-mini';
    
    applyLogicTransformationWithRowLimit(targetColumn, logic, columnElement, rowLimit, selectedModel);
} 