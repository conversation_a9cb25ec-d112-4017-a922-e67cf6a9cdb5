/**
 * Enhanced AI Mapping Handler
 * Handles the new auto-map functionality with checkbox-based mapping selection
 * and default logic settings for unmapped columns.
 */

let enhancedAiMappingModal = null;
let currentSuggestedMappings = [];
let defaultMappingSettings = null;

/**
 * Initialize the enhanced AI mapping modal
 */
function initializeEnhancedAiMapping() {
    if (document.getElementById('enhancedAiMappingModal')) {
        enhancedAiMappingModal = new bootstrap.Modal(document.getElementById('enhancedAiMappingModal'));
    }
    
    // Load default mapping settings
    loadDefaultMappingSettings();
    
    // Set up event handlers
    setupEnhancedAiMappingEventHandlers();
}

/**
 * Set up event handlers for the enhanced AI mapping modal
 */
function setupEnhancedAiMappingEventHandlers() {
    // Find Column Relations button
    $('#findColumnRelationsBtn').on('click', function() {
        findColumnRelations();
    });
    
    // Set Unmapped to Logic button
    $('#setUnmappedToLogicBtn').on('click', function() {
        setUnmappedColumnsToLogic();
    });
    
    // Remove Empty Column Mappings button
    $('#removeEmptyColumnMappingsBtn').on('click', function() {
        removeEmptyColumnMappings();
    });
    
    // Run All Mappings dropdown items
    $(document).on('click', '.dropdown-item[data-rows]', function(e) {
        e.preventDefault();
        const rowCount = parseInt($(this).data('rows'));
        runAllMappingsForRows(rowCount);
    });
    
    // Apply Selected Mappings button
    $('#applySelectedMappingsBtn').on('click', function() {
        applySelectedMappings();
    });
    
    // Select All checkbox
    $('#selectAllMappings').on('change', function() {
        const isChecked = $(this).is(':checked');
        $('#suggestedMappingsTableBody input[type="checkbox"]').prop('checked', isChecked);
        updateApplyButtonState();
    });
    
    // Individual checkbox changes
    $(document).on('change', '#suggestedMappingsTableBody input[type="checkbox"]', function() {
        updateSelectAllState();
        updateApplyButtonState();
    });
}

/**
 * Debug function to inspect current mapping definitions
 * Call this in browser console: debugMappingDefinitions()
 */
window.debugMappingDefinitions = function() {
    console.log('=== MAPPING DEFINITIONS DEBUG ===');
    console.log('Total mappingDefinitions:', mappingDefinitions?.length || 0);
    
    if (!mappingDefinitions || mappingDefinitions.length === 0) {
        console.log('No mappingDefinitions found!');
        return;
    }
    
    // Look specifically for Vorlagenartikel
    const vorlagenartikel = mappingDefinitions.find(m => m.column === 'Vorlagenartikel');
    if (vorlagenartikel) {
        console.log('Found Vorlagenartikel column:', vorlagenartikel);
    } else {
        console.log('Vorlagenartikel column NOT found');
    }
    
    // Show all columns with default_mapping
    const withDefaults = mappingDefinitions.filter(m => m.default_mapping);
    console.log('Columns with default_mapping:', withDefaults.length);
    withDefaults.forEach(m => {
        console.log(`- ${m.column}: type=${m.default_mapping.type}, content="${m.default_mapping.prompt_template || m.default_mapping.value || 'N/A'}"`);
    });
    
    // Show columns that have logic type specifically
    const logicColumns = mappingDefinitions.filter(m => m.default_mapping?.type === 'logic');
    console.log('Logic columns:', logicColumns.length);
    logicColumns.forEach(m => {
        console.log(`- ${m.column}: "${m.default_mapping.prompt_template}"`);
    });
    
    return {
        total: mappingDefinitions.length,
        withDefaults: withDefaults.length,
        logicColumns: logicColumns.length,
        vorlagenartikel: vorlagenartikel
    };
};

/**
 * Load default mapping settings from the server
 */
async function loadDefaultMappingSettings() {
    try {
        // Since we now use column-specific settings from mapping definitions,
        // we'll analyze the loaded mappings to show a summary
        if (mappingDefinitions && mappingDefinitions.length > 0) {
            console.log('Using existing mappingDefinitions for analysis');
            const defaultMappingSummary = analyzeDefaultMappings();
            
            // Update the UI with summary information
            $('#defaultMappingType').text(defaultMappingSummary.types.join(', '));
            $('#defaultMappingContent').text(defaultMappingSummary.description);
            
            defaultMappingSettings = {
                type: 'Mixed (from Google Sheets)',
                content: defaultMappingSummary.description
            };
        } else {
            console.log('No mappingDefinitions available, trying to fetch from server');
            // Fallback if no mapping definitions are loaded yet
            try {
                const response = await fetch('/api/import/get-default-mapping-settings');
                const data = await response.json();
                
                if (data.success) {
                    defaultMappingSettings = {
                        type: data.default_mapping_type,
                        content: data.default_mapping_content
                    };
                    
                    // Update the UI
                    $('#defaultMappingType').text(defaultMappingSettings.type);
                    $('#defaultMappingContent').text(defaultMappingSettings.content);
                } else {
                    console.warn('Could not load default mapping settings:', data.message);
                    setFallbackDefaults();
                }
            } catch (error) {
                console.error('Error loading default mapping settings:', error);
                setFallbackDefaults();
            }
        }
    } catch (error) {
        console.error('Error analyzing default mapping settings:', error);
        setFallbackDefaults();
    }
}

/**
 * Analyze the mapping definitions to summarize default mapping types
 */
function analyzeDefaultMappings() {
    const typeCounts = {
        'logic': 0,
        'one-to-one': 0,
        'string': 0,
        'deactivated': 0,
        'none': 0
    };
    
    const logicTemplates = new Set();
    const logicColumns = [];
    
    console.log('Analyzing mapping definitions:', mappingDefinitions.length, 'total columns');
    
    mappingDefinitions.forEach((mapping, index) => {
        const columnName = mapping.column;
        
        if (mapping.default_mapping) {
            const type = mapping.default_mapping.type;
            typeCounts[type] = (typeCounts[type] || 0) + 1;
            
            console.log(`Column ${columnName}: default_mapping type = ${type}`);
            
            if (type === 'logic' && mapping.default_mapping.prompt_template) {
                const template = mapping.default_mapping.prompt_template;
                logicTemplates.add(template);
                logicColumns.push({
                    name: columnName,
                    template: template
                });
                console.log(`Logic column found: ${columnName} with template: ${template}`);
            }
        } else {
            typeCounts['none']++;
            console.log(`Column ${columnName}: no default_mapping found`);
        }
    });
    
    console.log('Type counts:', typeCounts);
    console.log('Logic templates found:', Array.from(logicTemplates));
    console.log('Logic columns:', logicColumns);
    
    const types = [];
    const descriptions = [];
    
    if (typeCounts.logic > 0) {
        types.push(`Logic (${typeCounts.logic})`);
        if (logicTemplates.size === 1) {
            descriptions.push(`${typeCounts.logic} column${typeCounts.logic > 1 ? 's' : ''} use${typeCounts.logic === 1 ? 's' : ''} custom logic template`);
        } else if (logicTemplates.size > 1) {
            descriptions.push(`${typeCounts.logic} columns use ${logicTemplates.size} different logic templates`);
        } else {
            // Logic type but no templates found - should not happen
            descriptions.push(`${typeCounts.logic} logic columns (no templates detected)`);
        }
        
        // Add specific column names for logic mappings
        if (logicColumns.length > 0) {
            const columnNames = logicColumns.map(col => col.name).join(', ');
            descriptions.push(`Logic columns: ${columnNames}`);
        }
    }
    
    if (typeCounts['one-to-one'] > 0) {
        types.push(`One-to-One (${typeCounts['one-to-one']})`);
        descriptions.push(`${typeCounts['one-to-one']} columns have direct source mappings`);
    }
    
    if (typeCounts.string > 0) {
        types.push(`String (${typeCounts.string})`);
        descriptions.push(`${typeCounts.string} columns have static values`);
    }
    
    if (typeCounts.deactivated > 0) {
        types.push(`Deactivated (${typeCounts.deactivated})`);
        descriptions.push(`${typeCounts.deactivated} columns are deactivated`);
    }
    
    if (typeCounts.none > 0) {
        types.push(`No Default (${typeCounts.none})`);
        descriptions.push(`${typeCounts.none} columns will use global fallback`);
    }
    
    const result = {
        types: types.length > 0 ? types : ['No defaults configured'],
        description: descriptions.length > 0 ? descriptions.join('; ') : 'Column-specific defaults from Google Sheets configuration'
    };
    
    console.log('Analysis result:', result);
    return result;
}

/**
 * Set fallback default settings
 */
function setFallbackDefaults() {
    defaultMappingSettings = {
        type: 'Logic (fallback)',
        content: DEFAULT_LOGIC_TEMPLATE  // This now comes from the backend
    };
    $('#defaultMappingType').text(defaultMappingSettings.type);
    $('#defaultMappingContent').text(defaultMappingSettings.content);
}

/**
 * Open the enhanced AI mapping modal
 */
function openEnhancedAiMapping() {
    // First validate there's data to map
    if (!originalData || originalData.length === 0 || !mappingDefinitions || mappingDefinitions.length === 0) {
        showAlert('warning', 'Unable to perform AI mapping: No data or mapping definitions available.');
        return;
    }
    
    // Clear any previous results
    $('#suggestedMappingsContainer').addClass('d-none');
    $('#applySelectedMappingsBtn').addClass('d-none');
    currentSuggestedMappings = [];
    
    // Refresh default mapping settings in case mappingDefinitions have changed
    loadDefaultMappingSettings();
    
    // Populate the model dropdown
    if (typeof populateModelDropdown === 'function') {
        populateModelDropdown('enhancedAiModelDropdown', 'default_model_for_auto_map');
    } else {
        console.error("populateModelDropdown function is not available.");
        // Fallback or error display for model dropdown
        $('#enhancedAiModelDropdown').empty().append($('<option></option>').val('').text('Error loading models'));
    }
    
    // Show the modal
    if (enhancedAiMappingModal) {
        enhancedAiMappingModal.show();
    }
}

/**
 * Find 1 to 1 column relations using AI
 */
async function findColumnRelations() {
    const $btn = $('#findColumnRelationsBtn');
    const $spinner = $btn.find('.spinner-border'); // Assuming spinner is inside the button
    
    // Show loading state
    $btn.prop('disabled', true);
    $spinner.removeClass('d-none');
    
    try {
        // Get the model choice from the new dropdown
        const model = $('#enhancedAiModelDropdown').val();
        if (!model) {
            showAlert('error', 'Please select an LLM model for auto-mapping.');
            return;
        }
        
        // Prepare source column names for AI mapping
        const sourceColumnNames = sourceColumns.map(col => col.field);
        
        const requestData = {
            source_columns: sourceColumnNames,
            mapping_columns: mappingDefinitions,
            model: model, // Use selected model
            include_sample_data: true // This could be a toggle in the UI later
        };
        
        // Send to API for AI mapping
        const response = await fetch('/api/import/ai-map-columns', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(requestData)
        });
        
        if (!response.ok) {
            throw new Error('Network response was not ok');
        }
        
        const data = await response.json();
        
        if (data.success) {
            currentSuggestedMappings = data.mapped_columns;
            displaySuggestedMappings(currentSuggestedMappings);

            // Enhanced success message with performance metrics
            let successMessage = `Found ${currentSuggestedMappings.length} suggested column mappings`;
            if (data.processing_time) {
                successMessage += ` in ${data.processing_time}s`;
            }
            if (data.source_count && data.target_count) {
                successMessage += ` (${data.source_count} source → ${data.target_count} target columns)`;
            }

            showAlert('success', successMessage);

            // Log performance metrics for debugging
            console.log('🚀 Column mapping performance:', {
                processing_time: data.processing_time,
                source_count: data.source_count,
                target_count: data.target_count,
                mappings_found: data.mappings_found
            });
        } else {
            showAlert('error', data.message || 'Error during AI column mapping');
        }
    } catch (error) {
        console.error('Error finding column relations:', error);
        showAlert('error', 'Error finding column relations: ' + error.message);
    } finally {
        // Reset button state
        $btn.prop('disabled', false);
        $spinner.addClass('d-none');
    }
}

/**
 * Display suggested mappings in the table
 */
function displaySuggestedMappings(mappings) {
    const $container = $('#suggestedMappingsContainer');
    const $tableBody = $('#suggestedMappingsTableBody');
    
    // Clear existing content
    $tableBody.empty();
    
    if (!mappings || mappings.length === 0) {
        $container.addClass('d-none');
        return;
    }
    
    // Generate table rows
    mappings.forEach((mapping, index) => {
        const sourceColumn = mapping.source_column;
        const targetColumn = mapping.target_column;
        
        // Get example data for the source column
        const exampleData = getExampleDataForColumn(sourceColumn);
        
        // Get description for the target column
        const targetColumnDef = mappingDefinitions.find(def => def.column === targetColumn);
        const description = targetColumnDef ? (targetColumnDef.prompt || 'No description available') : 'No description available';
        
        // Enhanced display with method and confidence information
        const confidence = mapping.confidence || 'N/A';
        const method = mapping.method || 'ai';

        const confidenceClass = confidence !== 'N/A' && confidence >= 80 ? 'bg-success' :
                               confidence !== 'N/A' && confidence >= 60 ? 'bg-warning' : 'bg-secondary';

        const methodBadge = `<span class="badge bg-info ms-1">${method}</span>`;
        const confidenceBadge = confidence !== 'N/A' ?
            `<span class="badge ${confidenceClass}">${confidence}%</span>` :
            `<span class="badge bg-secondary">N/A</span>`;

        const row = `
            <tr data-mapping-index="${index}">
                <td>
                    <div class="form-check">
                        <input class="form-check-input mapping-checkbox" type="checkbox"
                               id="mapping_${index}" checked
                               data-source-column="${sourceColumn}"
                               data-target-column="${targetColumn}">
                        <label class="form-check-label" for="mapping_${index}"></label>
                    </div>
                </td>
                <td>
                    <strong>${sourceColumn}</strong>
                    <div class="text-muted small">${exampleData}</div>
                </td>
                <td>
                    <strong>${targetColumn}</strong>
                    <div class="mt-1">
                        ${confidenceBadge}
                        ${methodBadge}
                    </div>
                </td>
                <td class="small">${description}</td>
            </tr>
        `;
        
        $tableBody.append(row);
    });
    
    // Show the container and apply button
    $container.removeClass('d-none');
    $('#applySelectedMappingsBtn').removeClass('d-none');
    
    // Update states
    updateSelectAllState();
    updateApplyButtonState();
}

/**
 * Get example data for a source column
 */
function getExampleDataForColumn(columnName) {
    if (!originalData || originalData.length === 0) {
        return 'No data available';
    }
    
    const examples = [];
    for (let i = 0; i < Math.min(3, originalData.length); i++) {
        const value = originalData[i][columnName];
        if (value !== null && value !== undefined && value !== '') {
            examples.push(String(value));
        }
    }
    
    if (examples.length === 0) {
        return 'No data available';
    }
    
    return examples.join(', ');
}

/**
 * Update the select all checkbox state
 */
function updateSelectAllState() {
    const $checkboxes = $('#suggestedMappingsTableBody input[type="checkbox"]');
    const $selectAll = $('#selectAllMappings');
    
    if ($checkboxes.length === 0) {
        $selectAll.prop('indeterminate', false).prop('checked', false);
        return;
    }
    
    const checkedCount = $checkboxes.filter(':checked').length;
    
    if (checkedCount === 0) {
        $selectAll.prop('indeterminate', false).prop('checked', false);
    } else if (checkedCount === $checkboxes.length) {
        $selectAll.prop('indeterminate', false).prop('checked', true);
    } else {
        $selectAll.prop('indeterminate', true).prop('checked', false);
    }
}

/**
 * Update the apply button state
 */
function updateApplyButtonState() {
    const checkedCount = $('#suggestedMappingsTableBody input[type="checkbox"]:checked').length;
    const $applyBtn = $('#applySelectedMappingsBtn');
    
    if (checkedCount > 0) {
        $applyBtn.removeClass('d-none');
        $applyBtn.find('.fa-check').siblings().text(` Apply ${checkedCount} Selected Mapping${checkedCount > 1 ? 's' : ''}`);
    } else {
        $applyBtn.addClass('d-none');
    }
}

/**
 * Apply selected mappings to the UI
 */
async function applySelectedMappings() {
    const $btn = $('#applySelectedMappingsBtn');
    const $spinner = $('#applySelectedMappingsSpinner');
    
    // Show loading state
    $btn.prop('disabled', true);
    $spinner.removeClass('d-none');
    
    try {
        // Get selected mappings
        const selectedMappings = [];
        $('#suggestedMappingsTableBody input[type="checkbox"]:checked').each(function() {
            const sourceColumn = $(this).data('source-column');
            const targetColumn = $(this).data('target-column');
            selectedMappings.push({
                source_column: sourceColumn,
                target_column: targetColumn
            });
        });
        
        if (selectedMappings.length === 0) {
            showAlert('warning', 'No mappings selected');
            return;
        }
        
        // Apply the mappings using existing function
        applyAiMappings(selectedMappings);
        
        // Close the modal
        enhancedAiMappingModal.hide();
        
        // Save progress if we have a current job (debounced to prevent conflicts)
        if (currentJobId && typeof debouncedSaveCurrentJobProgress === 'function') {
            console.log("Auto-saving after applying selected mappings");
            debouncedSaveCurrentJobProgress(true); // Silent save
        }
        
        showAlert('success', `Applied ${selectedMappings.length} selected mappings`);
        
    } catch (error) {
        console.error('Error applying selected mappings:', error);
        showAlert('error', 'Error applying selected mappings: ' + error.message);
    } finally {
        // Reset button state
        $btn.prop('disabled', false);
        $spinner.addClass('d-none');
    }
}

/**
 * Set all unmapped columns to Logic with default prompt
 */
async function setUnmappedColumnsToLogic() {
    const $btn = $('#setUnmappedToLogicBtn');
    const $spinner = $('#setUnmappedToLogicSpinner');
    
    // Show loading state
    $btn.prop('disabled', true);
    $spinner.removeClass('d-none');
    
    try {
        // Find unmapped columns (columns that are not deactivated and don't have any mapping)
        const unmappedColumns = [];
        
        mappingDefinitions.forEach(targetCol => {
            const targetColumn = targetCol.column;
            
            // Skip if column is deactivated
            if (window.deactivatedColumns && window.deactivatedColumns[targetColumn]) {
                return;
            }
            
            // Skip if column already has a mapping
            if (columnMappings[targetColumn] || columnLogic[targetColumn] || columnStringValues[targetColumn]) {
                return;
            }
            
            unmappedColumns.push({
                name: targetColumn,
                definition: targetCol
            });
        });
        
        if (unmappedColumns.length === 0) {
            showAlert('info', 'No unmapped columns found to apply configuration-based mappings to');
            return;
        }
        
        console.log(`Processing ${unmappedColumns.length} unmapped columns...`);
        
        // Process columns in batches to prevent UI freezing
        const BATCH_SIZE = 10;
        let processedCount = 0;
        
        for (let i = 0; i < unmappedColumns.length; i += BATCH_SIZE) {
            const batch = unmappedColumns.slice(i, Math.min(i + BATCH_SIZE, unmappedColumns.length));
            
            // Process this batch
            batch.forEach(columnInfo => {
                const targetColumn = columnInfo.name;
                const columnDef = columnInfo.definition;
                
                // Check if this column has a specific default mapping configuration
                let logicTemplate = null;
                let mappingType = 'logic'; // Default to logic
                
                if (columnDef.default_mapping) {
                    const defaultMapping = columnDef.default_mapping;
                    
                    if (defaultMapping.type === 'logic' && defaultMapping.prompt_template) {
                        // Use column-specific logic template
                        logicTemplate = defaultMapping.prompt_template;
                        console.log(`Using column-specific logic template for ${targetColumn}: ${logicTemplate}`);
                    } else if (defaultMapping.type === 'deactivated') {
                        // Column should be deactivated, skip it
                        console.log(`Skipping ${targetColumn} - marked as deactivated in default mapping`);
                        return;
                    } else if (defaultMapping.type === 'one-to-one' && defaultMapping.source_column) {
                        // Set as one-to-one mapping instead of logic
                        columnMappings[targetColumn] = defaultMapping.source_column;
                        // Create column results immediately for one-to-one mappings
                        if (originalData && originalData.length > 0) {
                            columnResults[targetColumn] = originalData.map(row => 
                                row.hasOwnProperty(defaultMapping.source_column) ? row[defaultMapping.source_column] : null
                            );
                        }
                        console.log(`Set ${targetColumn} to one-to-one mapping with ${defaultMapping.source_column}`);
                        return;
                    } else if (defaultMapping.type === 'string' && defaultMapping.value !== undefined) {
                        // Set as string mapping instead of logic
                        columnStringValues[targetColumn] = defaultMapping.value;
                        console.log(`Set ${targetColumn} to string value: ${defaultMapping.value}`);
                        return;
                    }
                }
                
                // If no column-specific logic template, use the default logic template
                if (!logicTemplate) {
                    // Always use the DEFAULT_LOGIC_TEMPLATE for unmapped columns
                    // The defaultMappingSettings.content is just a description, not a template
                    logicTemplate = DEFAULT_LOGIC_TEMPLATE;
                    console.log(`Using default logic template for unmapped column ${targetColumn}`);
                }
                
                // Set the logic template
                columnLogic[targetColumn] = logicTemplate;
                
                // Initialize LLM settings for this column
                if (!columnLlmSettings[targetColumn]) {
                    columnLlmSettings[targetColumn] = {};
                }
                if (!columnLlmSettings[targetColumn].model) {
                    columnLlmSettings[targetColumn].model = 'gpt-4.1-mini'; // Default model
                }
                
                // Clear other mappings
                delete columnMappings[targetColumn];
                delete columnStringValues[targetColumn];
                
                processedCount++;
            });
            
            // Update UI after each batch with a small delay to prevent freezing
            if (i + BATCH_SIZE < unmappedColumns.length) {
                // Show progress
                const progress = Math.round((processedCount / unmappedColumns.length) * 100);
                showAlert('info', `Processing columns: ${progress}% (${processedCount}/${unmappedColumns.length})`);
                
                // Small delay to allow UI to update
                await new Promise(resolve => setTimeout(resolve, 50));
            }
        }
        
        // Update the UI to reflect all changes after processing is complete
        console.log(`Finished processing ${processedCount} columns, updating UI...`);
        
        // Batch UI updates to prevent freezing
        const columnsToUpdate = unmappedColumns.map(col => col.name);
        const UI_UPDATE_BATCH_SIZE = 20;
        
        for (let i = 0; i < columnsToUpdate.length; i += UI_UPDATE_BATCH_SIZE) {
            const uiBatch = columnsToUpdate.slice(i, Math.min(i + UI_UPDATE_BATCH_SIZE, columnsToUpdate.length));
            
            // Update column displays for this batch
            uiBatch.forEach(targetColumn => {
                if (typeof refreshTargetColumnDisplay === 'function') {
                    refreshTargetColumnDisplay(targetColumn);
                }
            });
            
            // Small delay between UI update batches
            if (i + UI_UPDATE_BATCH_SIZE < columnsToUpdate.length) {
                await new Promise(resolve => setTimeout(resolve, 30));
            }
        }
        
        // Final UI update
        setTimeout(() => {
            if (typeof updateUnifiedPreviewGrid === 'function') {
                updateUnifiedPreviewGrid();
            }
        }, 100);
        
        // Save progress if we have a current job (debounced to prevent conflicts)
        if (currentJobId && typeof debouncedSaveCurrentJobProgress === 'function') {
            console.log("Auto-saving after applying configuration-based mappings");
            console.log(`🎯 Before save - Global mapping state:`, {
                column_mappings: Object.keys(window.columnMappings || {}).length,
                column_logic: Object.keys(window.columnLogic || {}).length,
                column_string_values: Object.keys(window.columnStringValues || {}).length
            });
            debouncedSaveCurrentJobProgress(true); // Silent save
        }
        
        showAlert('success', `Applied configuration-based mappings to ${processedCount} unmapped columns (${unmappedColumns.filter(col => columnLogic[col.name]).length} logic, ${unmappedColumns.filter(col => columnMappings[col.name]).length} one-to-one, ${unmappedColumns.filter(col => columnStringValues[col.name]).length} string)`);
        
    } catch (error) {
        console.error('Error applying configuration-based mappings:', error);
        showAlert('error', 'Error applying configuration-based mappings: ' + error.message);
    } finally {
        // Reset button state
        $btn.prop('disabled', false);
        $spinner.addClass('d-none');
    }
}

/**
 * Run all mappings for a specified number of rows
 * This function runs logic for all columns that are auto mapped or have mappings applied but not run yet
 * It respects the row limit (1 or 3 rows max) and skips cells that are already mapped
 */
async function runAllMappingsForRows(rowCount) {
    if (!rowCount || (rowCount !== 1 && rowCount !== 3)) {
        showAlert('error', 'Invalid row count. Only 1 or 3 rows are supported.');
        return;
    }

    const $btn = $('#runAllMappingsBtn');
    const $spinner = $btn.find('.spinner-border');

    $btn.prop('disabled', true);
    $spinner.removeClass('d-none');

    // Reset or initialize columnResults for the specified number of rows if not already done
    if (!columnResults) columnResults = {};
    mappingDefinitions.forEach(def => {
        if (!columnResults[def.column] || columnResults[def.column].length < rowCount) {
            const existingValues = columnResults[def.column] || [];
            columnResults[def.column] = Array(rowCount).fill(null).map((_, i) => existingValues[i] || null);
        }
    });

    const results = { oneToOne: 0, string: 0, logic: 0, errors: 0, logicCellsProcessed: 0 };
    let overallSuccess = true;

    try {
        const currentNotes = $('#userNotes').val() || userNotes;
        const logicColumnsToProcess = [];

        Object.keys(columnLogic).forEach(targetColumn => {
            const needsProcessing = !columnResults[targetColumn] || 
                columnResults[targetColumn].some((val, idx) => {
                    if (idx >= rowCount) return false;
                    return val === undefined || val === null || val === '' || 
                           (typeof val === 'string' && val.startsWith('Error:'));
                });
            if (needsProcessing) {
                logicColumnsToProcess.push({
                    targetColumn: targetColumn,
                    logic: columnLogic[targetColumn]
                });
            }
        });

        const grandTotalLogicCells = logicColumnsToProcess.length * rowCount;
        let cumulativeLogicCellsProcessed = 0;

        if (grandTotalLogicCells > 0 && bulkProgressManager && typeof bulkProgressManager.show === 'function') {
            bulkProgressManager.show(
                "RunAllLogicMappings", // Generic Job ID for cancellation, not column specific
                grandTotalLogicCells, 
                "run_all_logic_mappings_job"
            );
        }

        // Process one-to-one mappings (fast, no individual progress)
        Object.keys(columnMappings).forEach(targetColumn => {
            try {
                if (!columnResults[targetColumn]) columnResults[targetColumn] = Array(rowCount).fill(null);
                for (let i = 0; i < rowCount && i < originalData.length; i++) {
                    if (columnResults[targetColumn][i] === undefined || columnResults[targetColumn][i] === null || columnResults[targetColumn][i] === '') {
                        const sourceValue = originalData[i][columnMappings[targetColumn]];
                        columnResults[targetColumn][i] = sourceValue !== undefined && sourceValue !== null ? sourceValue : '';
                    }
                }
                results.oneToOne++;
            } catch (e) {
                console.error(`Error in one-to-one mapping for ${targetColumn}: ${e.message}`);
                results.errors++;
                overallSuccess = false;
            }
        });
        
        // Process string mappings (fast, no individual progress)
        Object.keys(columnStringValues).forEach(targetColumn => {
            try {
                if (!columnResults[targetColumn]) columnResults[targetColumn] = Array(rowCount).fill(null);
                for (let i = 0; i < rowCount && i < originalData.length; i++) {
                    if (columnResults[targetColumn][i] === undefined || columnResults[targetColumn][i] === null || columnResults[targetColumn][i] === '') {
                        columnResults[targetColumn][i] = columnStringValues[targetColumn];
                    }
                }
                results.string++;
            } catch (e) {
                console.error(`Error in string mapping for ${targetColumn}: ${e.message}`);
                results.errors++;
                overallSuccess = false;
            }
        });

        // Process logic mappings (most complex, use unified progress)
        if (logicColumnsToProcess.length > 0) {
            const selectedModel = $('#enhancedAiModelDropdown').val();
            if (!selectedModel) {
                showAlert('error', 'Please select an LLM model to run logic mappings.');
                if (grandTotalLogicCells > 0 && bulkProgressManager) bulkProgressManager.complete(false, "Model not selected.");
                $btn.prop('disabled', false); $spinner.addClass('d-none');
                return; 
            }
            
            for (const columnInfo of logicColumnsToProcess) {
                if (bulkProgressManager && bulkProgressManager.isCancelled()) {
                    showAlert('warning', 'Bulk mapping run cancelled by user.');
                    overallSuccess = false; // Mark as not fully successful due to cancellation
                    break; 
                }
                try {
                    const targetColumn = columnInfo.targetColumn;
                    const logic = columnInfo.logic;
                    const columnElement = $(`.mapping-column-cell[data-target-column="${targetColumn}"]`);
                    const prompt = columnElement.attr('data-prompt') || '';
                    
                    console.log(`Processing logic for ${targetColumn} with ${rowCount} rows using model ${selectedModel} (Unified Progress)`);
                    
                    const jobSegmentResult = await callBulkApplyLogicAndUpdateClient(
                        targetColumn, 
                        logic, 
                        currentNotes, 
                        prompt, 
                        rowCount, 
                        selectedModel, 
                        columnElement,
                        { showProgressUI: false } // Key change: run this segment silently
                    );
                    
                    if (jobSegmentResult.success) {
                        // Count the actual cells processed for this specific column
                        const actualCellsForThisColumn = Math.min(jobSegmentResult.processedCells, rowCount);
                        cumulativeLogicCellsProcessed += actualCellsForThisColumn;
                        results.logicCellsProcessed += actualCellsForThisColumn;
                        
                        console.log(`Column ${targetColumn}: processed ${actualCellsForThisColumn} cells, cumulative: ${cumulativeLogicCellsProcessed}/${grandTotalLogicCells}`);
                    } else {
                        // If the segment itself reported an error, count it
                        results.errors++;
                        overallSuccess = false;
                        if (grandTotalLogicCells > 0 && bulkProgressManager && typeof bulkProgressManager.addLogEntry === 'function') {
                            bulkProgressManager.addLogEntry(`Error processing ${targetColumn}: ${jobSegmentResult.error || 'Unknown error'}`, 'error');
                        }
                    }
                    
                    if (grandTotalLogicCells > 0 && bulkProgressManager && typeof bulkProgressManager.updateProgress === 'function') {
                        bulkProgressManager.updateProgress(cumulativeLogicCellsProcessed);
                    }
                    results.logic++;

                } catch (error) {
                    // Catch errors thrown by callBulkApplyLogicAndUpdateClient (e.g., network, severe issues)
                    console.error(`Critical error processing logic mapping for ${columnInfo.targetColumn}:`, error);
                    results.errors++;
                    overallSuccess = false;
                    if (grandTotalLogicCells > 0 && bulkProgressManager && typeof bulkProgressManager.addLogEntry === 'function') {
                        bulkProgressManager.addLogEntry(`Critical error for ${columnInfo.targetColumn}: ${error.message}`, 'error');
                    }
                }
                
                if (logicColumnsToProcess.indexOf(columnInfo) < logicColumnsToProcess.length - 1) {
                    if (bulkProgressManager && bulkProgressManager.isCancelled()) break;
                    await new Promise(resolve => setTimeout(resolve, 100)); // Shorter delay, just to yield CPU
                }
            }
        }
        
        if (grandTotalLogicCells > 0 && bulkProgressManager && typeof bulkProgressManager.complete === 'function') {
            if (bulkProgressManager.isCancelled()) {
                bulkProgressManager.complete(false, `Bulk mapping run cancelled by user after processing approx. ${cumulativeLogicCellsProcessed} logic cells.`);
            } else if (!overallSuccess || results.errors > 0) {
                 bulkProgressManager.complete(false, `Bulk mapping run completed with ${results.errors} error(s) during logic processing. Processed ${results.logicCellsProcessed} logic cells.`);
            } else {
                 bulkProgressManager.complete(true, `Bulk mapping run completed. Processed ${results.logicCellsProcessed} logic cells.`);
            }
        }
        
        if (typeof updateUnifiedPreviewGrid === 'function') {
            updateUnifiedPreviewGrid();
        }
        
        // Save progress after completing run all mappings (this is a completion save, not auto-save)
        if (currentJobId && typeof saveCurrentJobProgress === 'function') {
            saveCurrentJobProgress(true); // Silent save after completion
        }
        
        //showAlert('success', `Run All Mappings complete. Processed: ${results.oneToOne} one-to-one, ${results.string} string, ${results.logic} logic mappings (${results.logicCellsProcessed} logic cells). Errors: ${results.errors}.`);
        // A more subtle log or a clear completion state in the UI might be better than an alert here.
        console.log(`Run All Mappings complete. Processed: ${results.oneToOne} one-to-one, ${results.string} string, ${results.logic} logic mappings (${results.logicCellsProcessed} logic cells). Errors: ${results.errors}.`);

    } catch (mainError) {
        console.error("Main error in runAllMappingsForRows:", mainError);
        if (!(mainError.message && mainError.message.toLowerCase().includes('cancel'))) {
             showAlert('error', `An unexpected error occurred during Run All Mappings: ${mainError.message}`);
        }
        if (grandTotalLogicCells > 0 && bulkProgressManager && typeof bulkProgressManager.complete === 'function' && bulkProgressManager.isActive) {
            bulkProgressManager.complete(false, `Operation failed due to an unexpected error: ${mainError.message}`);
        }
        // overallSuccess = false; // Already handled if error is thrown
    } finally {
        // Ensure the button is re-enabled and spinner is hidden, regardless of outcome.
        $btn.prop('disabled', false);
        $spinner.addClass('d-none');
        console.log("'Run All Mappings' button state reset in finally block.");

        // Optional: Check if the enhanced AI mapping modal should be closed
        // For now, let the user close it manually.
        // if (overallSuccess && results.errors === 0 && enhancedAiMappingModal && !bulkProgressManager.isActive) {
        //     enhancedAiMappingModal.hide(); 
        // }
    }
}

/**
 * Remove mappings for columns where all cells are empty
 * This function checks all source columns in the original data and removes any mappings
 * for columns that contain only empty, null, or undefined values
 */
async function removeEmptyColumnMappings() {
    const $btn = $('#removeEmptyColumnMappingsBtn');
    const $spinner = $('#removeEmptyColumnMappingsSpinner');
    
    // Show loading state
    $btn.prop('disabled', true);
    $spinner.removeClass('d-none');
    
    try {
        if (!originalData || originalData.length === 0) {
            showAlert('warning', 'No data available to analyze for empty columns.');
            return;
        }
        
        console.log('Analyzing columns for empty data...');
        
        // Get all source column names from the first row of data
        const sourceColumns = Object.keys(originalData[0] || {});
        
        if (sourceColumns.length === 0) {
            showAlert('warning', 'No source columns found in the data.');
            return;
        }
        
        // Find columns that are completely empty
        const emptyColumns = [];
        
        sourceColumns.forEach(columnName => {
            let hasNonEmptyValue = false;
            
            // Check all rows for this column
            for (let i = 0; i < originalData.length; i++) {
                const value = originalData[i][columnName];
                
                // Consider a value non-empty if it's not null, undefined, empty string, or just whitespace
                if (value !== null && value !== undefined && value !== '' && 
                    (typeof value !== 'string' || value.trim() !== '')) {
                    hasNonEmptyValue = true;
                    break;
                }
            }
            
            if (!hasNonEmptyValue) {
                emptyColumns.push(columnName);
            }
        });
        
        console.log(`Found ${emptyColumns.length} empty columns:`, emptyColumns);
        
        if (emptyColumns.length === 0) {
            showAlert('info', 'No completely empty columns found. All source columns contain at least some data.');
            return;
        }
        
        // Find target columns that are mapped to these empty source columns
        const mappingsToRemove = [];
        
        // Check one-to-one mappings
        Object.keys(columnMappings).forEach(targetColumn => {
            const sourceColumn = columnMappings[targetColumn];
            if (emptyColumns.includes(sourceColumn)) {
                mappingsToRemove.push({
                    targetColumn: targetColumn,
                    sourceColumn: sourceColumn,
                    type: 'one-to-one'
                });
            }
        });
        
        if (mappingsToRemove.length === 0) {
            showAlert('info', `Found ${emptyColumns.length} empty source columns, but none of them are currently mapped to target columns.`);
            return;
        }
        
        // Show confirmation dialog
        const confirmMessage = `Found ${mappingsToRemove.length} target column(s) mapped to empty source columns:\n\n` +
            mappingsToRemove.map(mapping => `• ${mapping.targetColumn} ← ${mapping.sourceColumn}`).join('\n') +
            `\n\nDo you want to remove these mappings?`;
        
        if (!confirm(confirmMessage)) {
            showAlert('info', 'Operation cancelled by user.');
            return;
        }
        
        // Remove the mappings
        let removedCount = 0;
        
        mappingsToRemove.forEach(mapping => {
            const targetColumn = mapping.targetColumn;
            
            // Remove from columnMappings
            if (columnMappings[targetColumn]) {
                delete columnMappings[targetColumn];
                removedCount++;
            }
            
            // Clear any existing results for this column
            if (columnResults[targetColumn]) {
                delete columnResults[targetColumn];
            }
            
            // Update the UI for this column
            if (typeof refreshTargetColumnDisplay === 'function') {
                refreshTargetColumnDisplay(targetColumn);
            }
            
            console.log(`Removed mapping: ${targetColumn} ← ${mapping.sourceColumn}`);
        });
        
        // Update the unified preview grid
        if (typeof updateUnifiedPreviewGrid === 'function') {
            updateUnifiedPreviewGrid();
        }
        
        // Save progress if we have a current job (debounced to prevent conflicts)
        if (currentJobId && typeof debouncedSaveCurrentJobProgress === 'function') {
            console.log("Auto-saving after removing empty column mappings");
            debouncedSaveCurrentJobProgress(true); // Silent save
        }
        
        // Show success message
        const emptyColumnsList = emptyColumns.length <= 5 ? 
            emptyColumns.join(', ') : 
            `${emptyColumns.slice(0, 5).join(', ')} and ${emptyColumns.length - 5} more`;
            
        showAlert('success', 
            `Removed ${removedCount} mapping(s) for columns with empty data.\n\n` +
            `Empty source columns found: ${emptyColumnsList}`
        );
        
    } catch (error) {
        console.error('Error removing empty column mappings:', error);
        showAlert('error', 'Error removing empty column mappings: ' + error.message);
    } finally {
        // Reset button state
        $btn.prop('disabled', false);
        $spinner.addClass('d-none');
    }
}

// Initialize when document is ready
$(document).ready(function() {
    initializeEnhancedAiMapping();
}); 